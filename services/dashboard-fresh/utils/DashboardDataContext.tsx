// Dashboard Data Context - Week 17-18 Implementation
// Shared state management and real-time data coordination for unified analytics dashboard
// Provides synchronized updates across all D3.js visualization components

import { createContext } from "preact";
import { useContext, useEffect } from "preact/hooks";
import { useSignal, computed, Signal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import { useRealtimeData } from "./useRealtimeData.ts";
import type { RealtimeMetricsData } from "../islands/RealtimeMetricsStream.tsx";
import type { ChurnGaugeData } from "../islands/charts/D3ChurnGauge.tsx";
import type { CohortHeatmapData } from "../islands/charts/D3CohortHeatmap.tsx";
import type { CLVHistogramData, CLVStatistics } from "../islands/charts/D3CLVHistogram.tsx";
import type { FunnelStepData } from "../islands/charts/D3FunnelChart.tsx";
import type { RevenueForecastData } from "../islands/charts/D3RevenueForecasting.tsx";
import type { SankeyFlowData } from "../islands/charts/D3SankeyFlow.tsx";

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface DashboardData {
  // Real-time metrics
  totalRevenue: number;
  totalOrders: number;
  conversionRate: number;
  avgOrderValue: number;
  activeUsers: number;
  
  // Churn analysis
  churnData: ChurnGaugeData | null;
  
  // Cohort analysis
  cohortHeatmapData: CohortHeatmapData[];
  cohortComparisonData: CohortHeatmapData[];
  
  // CLV analysis
  clvHistogramData: CLVHistogramData[];
  clvStatistics: CLVStatistics | null;
  
  // Funnel analysis
  funnelData: FunnelStepData[];
  
  // Revenue forecasting
  revenueForecastData: RevenueForecastData[];
  
  // Customer journey
  sankeyFlowData: SankeyFlowData | null;
  
  // Metadata
  lastUpdated: Date | null;
  isLoading: boolean;
  error: string | null;
}

export interface DashboardFilters {
  dateRange: {
    start: Date;
    end: Date;
  };
  cohortPeriod: 'daily' | 'weekly' | 'monthly';
  customerSegment: 'all' | 'new' | 'returning' | 'high-value';
  region: string | null;
  productCategory: string | null;
}

export interface DashboardSettings {
  enableRealtimeUpdates: boolean;
  updateInterval: number;
  animationDuration: number;
  showAdvancedMetrics: boolean;
  compactMode: boolean;
  darkMode: boolean;
}

export interface DashboardContextValue {
  // Data
  data: Signal<DashboardData>;
  filters: Signal<DashboardFilters>;
  settings: Signal<DashboardSettings>;
  
  // Real-time connection
  isConnected: boolean;
  connectionLatency: number;
  lastUpdate: Date | null;
  
  // Actions
  updateFilters: (filters: Partial<DashboardFilters>) => void;
  updateSettings: (settings: Partial<DashboardSettings>) => void;
  refreshData: () => void;
  exportData: (format: 'csv' | 'json' | 'pdf') => Promise<void>;
  
  // Component-specific data getters
  getChurnData: () => ChurnGaugeData | null;
  getCohortData: () => CohortHeatmapData[];
  getCLVData: () => { data: CLVHistogramData[]; statistics: CLVStatistics | null };
  getFunnelData: () => FunnelStepData[];
  getRevenueData: () => RevenueForecastData[];
  getSankeyData: () => SankeyFlowData | null;
}

// =====================================================
// CONTEXT CREATION
// =====================================================

const DashboardContext = createContext<DashboardContextValue | null>(null);

// =====================================================
// DATA TRANSFORMATION UTILITIES
// =====================================================

/**
 * Transform real-time metrics to dashboard data format
 */
function transformRealtimeData(realtimeData: RealtimeMetricsData): Partial<DashboardData> {
  const { payload, trends } = realtimeData;
  
  return {
    totalRevenue: payload.totalRevenue,
    totalOrders: payload.totalOrders,
    conversionRate: payload.conversionRate,
    avgOrderValue: payload.avgOrderValue,
    activeUsers: payload.activeUsers,
    lastUpdated: new Date(realtimeData.timestamp),
  };
}

/**
 * Transform real-time churn data to ChurnGaugeData format
 */
function transformChurnData(realtimeData: RealtimeMetricsData): ChurnGaugeData | null {
  if (!realtimeData.payload.churnRisk) return null;
  
  const churnRisk = realtimeData.payload.churnRisk;
  const totalCustomers = realtimeData.payload.activeUsers;
  
  return {
    totalCustomers,
    avgChurnProbability: churnRisk.avgProbability,
    highRiskThreshold: 0.7,
    criticalRiskThreshold: 0.85,
    riskDistribution: [
      {
        riskLevel: 'low',
        customerCount: Math.max(0, totalCustomers - churnRisk.highRiskCount - churnRisk.criticalRiskCount),
        percentage: 0.6,
        avgChurnProbability: 0.15,
        interventionRequired: false
      },
      {
        riskLevel: 'medium',
        customerCount: Math.floor((totalCustomers - churnRisk.highRiskCount - churnRisk.criticalRiskCount) * 0.3),
        percentage: 0.25,
        avgChurnProbability: 0.45,
        interventionRequired: false
      },
      {
        riskLevel: 'high',
        customerCount: churnRisk.highRiskCount,
        percentage: 0.12,
        avgChurnProbability: 0.75,
        interventionRequired: true
      },
      {
        riskLevel: 'critical',
        customerCount: churnRisk.criticalRiskCount,
        percentage: 0.03,
        avgChurnProbability: 0.92,
        interventionRequired: true
      }
    ]
  };
}

/**
 * Generate mock data for components not yet integrated with real-time
 */
function generateMockData(tenantId: string): Partial<DashboardData> {
  // This would be replaced with actual API calls in production
  return {
    cohortHeatmapData: generateMockCohortData(),
    clvHistogramData: generateMockCLVData(),
    funnelData: generateMockFunnelData(),
    revenueForecastData: generateMockRevenueData(),
    sankeyFlowData: generateMockSankeyData(),
  };
}

function generateMockCohortData(): CohortHeatmapData[] {
  const data: CohortHeatmapData[] = [];
  const cohortDates = ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05'];
  
  cohortDates.forEach((date, cohortIndex) => {
    for (let period = 0; period < 12; period++) {
      data.push({
        cohortId: `cohort-${cohortIndex}`,
        cohortDate: date,
        period,
        retentionRate: Math.max(0.1, 0.8 - (period * 0.05) + (Math.random() * 0.1)),
        customerCount: Math.floor(1000 * (0.8 - period * 0.05))
      });
    }
  });
  
  return data;
}

function generateMockCLVData(): CLVHistogramData[] {
  const ranges = [
    { min: 0, max: 100, label: '$0-$100' },
    { min: 100, max: 250, label: '$100-$250' },
    { min: 250, max: 500, label: '$250-$500' },
    { min: 500, max: 1000, label: '$500-$1K' },
    { min: 1000, max: 2500, label: '$1K-$2.5K' },
    { min: 2500, max: 5000, label: '$2.5K-$5K' },
    { min: 5000, max: 10000, label: '$5K-$10K' },
  ];
  
  return ranges.map((range, index) => ({
    clvRange: range.label,
    customerCount: Math.floor(Math.random() * 500) + 50,
    totalClv: Math.floor(Math.random() * 100000) + 10000,
    percentage: Math.random() * 0.3 + 0.05,
    rangeMin: range.min,
    rangeMax: range.max,
  }));
}

function generateMockFunnelData(): FunnelStepData[] {
  return [
    { stepId: 'awareness', stepName: 'Awareness', stepOrder: 1, totalUsers: 10000, conversionRate: 1.0, dropoffRate: 0.0 },
    { stepId: 'interest', stepName: 'Interest', stepOrder: 2, totalUsers: 7500, conversionRate: 0.75, dropoffRate: 0.25 },
    { stepId: 'consideration', stepName: 'Consideration', stepOrder: 3, totalUsers: 4500, conversionRate: 0.6, dropoffRate: 0.4 },
    { stepId: 'intent', stepName: 'Purchase Intent', stepOrder: 4, totalUsers: 2250, conversionRate: 0.5, dropoffRate: 0.5 },
    { stepId: 'purchase', stepName: 'Purchase', stepOrder: 5, totalUsers: 1125, conversionRate: 0.5, dropoffRate: 0.5 },
  ];
}

function generateMockRevenueData(): RevenueForecastData[] {
  const data: RevenueForecastData[] = [];
  const startDate = new Date('2024-01-01');
  
  // Historical data (6 months)
  for (let i = 0; i < 6; i++) {
    const date = new Date(startDate);
    date.setMonth(date.getMonth() + i);
    
    const baseRevenue = 100000 + (i * 5000) + (Math.random() * 10000);
    data.push({
      date: date.toISOString().split('T')[0],
      actualRevenue: baseRevenue,
      predictedRevenue: baseRevenue,
      confidenceLower: baseRevenue * 0.9,
      confidenceUpper: baseRevenue * 1.1,
      forecastType: 'historical'
    });
  }
  
  // Forecast data (6 months)
  for (let i = 6; i < 12; i++) {
    const date = new Date(startDate);
    date.setMonth(date.getMonth() + i);
    
    const predictedRevenue = 100000 + (i * 6000) + (Math.random() * 5000);
    data.push({
      date: date.toISOString().split('T')[0],
      predictedRevenue,
      confidenceLower: predictedRevenue * 0.85,
      confidenceUpper: predictedRevenue * 1.15,
      forecastType: 'predicted'
    });
  }
  
  return data;
}

function generateMockSankeyData(): SankeyFlowData {
  return {
    nodes: [
      { id: 'social', name: 'Social Media', stage: 'awareness', value: 5000 },
      { id: 'search', name: 'Search', stage: 'awareness', value: 3000 },
      { id: 'email', name: 'Email', stage: 'awareness', value: 2000 },
      { id: 'website', name: 'Website Visit', stage: 'interest', value: 7000 },
      { id: 'product', name: 'Product Page', stage: 'consideration', value: 4000 },
      { id: 'cart', name: 'Add to Cart', stage: 'intent', value: 2000 },
      { id: 'checkout', name: 'Checkout', stage: 'purchase', value: 1000 },
    ],
    links: [
      { source: 'social', target: 'website', value: 3500 },
      { source: 'search', target: 'website', value: 2500 },
      { source: 'email', target: 'website', value: 1000 },
      { source: 'website', target: 'product', value: 4000 },
      { source: 'product', target: 'cart', value: 2000 },
      { source: 'cart', target: 'checkout', value: 1000 },
    ]
  };
}

// =====================================================
// DASHBOARD DATA PROVIDER
// =====================================================

export interface DashboardDataProviderProps {
  tenantId: string;
  initialData?: Partial<DashboardData>;
  children: preact.ComponentChildren;
}

export function DashboardDataProvider({
  tenantId,
  initialData = {},
  children
}: DashboardDataProviderProps) {
  // Initialize signals
  const data = useSignal<DashboardData>({
    totalRevenue: 0,
    totalOrders: 0,
    conversionRate: 0,
    avgOrderValue: 0,
    activeUsers: 0,
    churnData: null,
    cohortHeatmapData: [],
    cohortComparisonData: [],
    clvHistogramData: [],
    clvStatistics: null,
    funnelData: [],
    revenueForecastData: [],
    sankeyFlowData: null,
    lastUpdated: null,
    isLoading: true,
    error: null,
    ...initialData,
  });

  const filters = useSignal<DashboardFilters>({
    dateRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      end: new Date(),
    },
    cohortPeriod: 'weekly',
    customerSegment: 'all',
    region: null,
    productCategory: null,
  });

  const settings = useSignal<DashboardSettings>({
    enableRealtimeUpdates: true,
    updateInterval: 5000,
    animationDuration: 750,
    showAdvancedMetrics: true,
    compactMode: false,
    darkMode: false,
  });

  // Real-time data integration
  const {
    data: _realtimeData,
    isConnected,
    latency,
    lastUpdate,
  } = useRealtimeData({
    tenantId,
    dataTypes: ['metrics', 'predictions'],
    updateInterval: settings.value.updateInterval,
    enableSmoothing: true,
    onUpdate: (newData) => {
      // Transform and update dashboard data
      const transformedData = transformRealtimeData(newData);
      const churnData = transformChurnData(newData);

      data.value = {
        ...data.value,
        ...transformedData,
        churnData,
        isLoading: false,
        error: null,
      };
    },
  });

  // Load initial mock data
  useEffect(() => {
    if (IS_BROWSER) {
      const mockData = generateMockData(tenantId);
      data.value = {
        ...data.value,
        ...mockData,
        isLoading: false,
      };
    }
  }, [tenantId]);

  // Actions
  const updateFilters = (newFilters: Partial<DashboardFilters>) => {
    filters.value = { ...filters.value, ...newFilters };
  };

  const updateSettings = (newSettings: Partial<DashboardSettings>) => {
    settings.value = { ...settings.value, ...newSettings };
  };

  const refreshData = () => {
    data.value = { ...data.value, isLoading: true, error: null };

    try {
      // In production, this would fetch fresh data from APIs
      const mockData = generateMockData(tenantId);
      data.value = {
        ...data.value,
        ...mockData,
        lastUpdated: new Date(),
        isLoading: false,
      };
    } catch (error) {
      data.value = {
        ...data.value,
        error: error instanceof Error ? error.message : 'Failed to refresh data',
        isLoading: false,
      };
    }
  };

  const exportData = (format: 'csv' | 'json' | 'pdf') => {
    // Implementation would depend on export requirements
    console.log(`Exporting dashboard data as ${format}`);
    return Promise.resolve();
  };

  // Component-specific data getters
  const getChurnData = () => data.value.churnData;
  const getCohortData = () => data.value.cohortHeatmapData;
  const getCLVData = () => ({
    data: data.value.clvHistogramData,
    statistics: data.value.clvStatistics
  });
  const getFunnelData = () => data.value.funnelData;
  const getRevenueData = () => data.value.revenueForecastData;
  const getSankeyData = () => data.value.sankeyFlowData;

  const contextValue: DashboardContextValue = {
    data,
    filters,
    settings,
    isConnected,
    connectionLatency: latency,
    lastUpdate,
    updateFilters,
    updateSettings,
    refreshData,
    exportData,
    getChurnData,
    getCohortData,
    getCLVData,
    getFunnelData,
    getRevenueData,
    getSankeyData,
  };

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  );
}

// =====================================================
// DASHBOARD DATA HOOK
// =====================================================

export function useDashboardData(): DashboardContextValue {
  const context = useContext(DashboardContext);

  if (!context) {
    throw new Error('useDashboardData must be used within a DashboardDataProvider');
  }

  return context;
}

// =====================================================
// COMPUTED VALUES
// =====================================================

export function useDashboardMetrics() {
  const { data } = useDashboardData();

  return computed(() => {
    const currentData = data.value;

    return {
      // Primary KPIs
      totalRevenue: currentData.totalRevenue,
      totalOrders: currentData.totalOrders,
      conversionRate: currentData.conversionRate,
      avgOrderValue: currentData.avgOrderValue,
      activeUsers: currentData.activeUsers,

      // Derived metrics
      revenuePerUser: currentData.activeUsers > 0
        ? currentData.totalRevenue / currentData.activeUsers
        : 0,

      // Status indicators
      hasChurnData: !!currentData.churnData,
      hasCohortData: currentData.cohortHeatmapData.length > 0,
      hasCLVData: currentData.clvHistogramData.length > 0,
      hasFunnelData: currentData.funnelData.length > 0,
      hasRevenueData: currentData.revenueForecastData.length > 0,
      hasSankeyData: !!currentData.sankeyFlowData,

      // Meta information
      lastUpdated: currentData.lastUpdated,
      isLoading: currentData.isLoading,
      error: currentData.error,
    };
  });
}
