// Dashboard Grid Component - Week 17-18 Implementation
// Responsive grid layout containing all D3.js visualization components with coordinated updates

import { useSignal } from "@preact/signals";
import { useDashboardData, useDashboardMetrics } from "../utils/DashboardDataContext.tsx";
import D3ChurnGaugeRealtime from "./charts/D3ChurnGaugeRealtime.tsx";
import D3CohortHeatmap from "./charts/D3CohortHeatmap.tsx";
import D3CLVHistogram from "./charts/D3CLVHistogram.tsx";
import D3FunnelChart from "./charts/D3FunnelChart.tsx";
import D3RevenueForecasting from "./charts/D3RevenueForecasting.tsx";
import D3SankeyFlow from "./charts/D3SankeyFlow.tsx";

export default function DashboardGrid() {
  const { 
    settings,
    getChurnData,
    getCohortData,
    getCLVData,
    getFunnelData,
    getRevenueData,
    getSankeyData
  } = useDashboardData();
  
  const metrics = useDashboardMetrics();
  const expandedChart = useSignal<string | null>(null);

  const handleChartExpand = (chartId: string) => {
    expandedChart.value = expandedChart.value === chartId ? null : chartId;
  };

  const isCompact = settings.value.compactMode;
  const showAdvanced = settings.value.showAdvancedMetrics;

  // Grid layout classes based on compact mode
  const gridClasses = isCompact 
    ? "grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 p-4"
    : "grid grid-cols-1 lg:grid-cols-2 gap-6 p-6";

  const chartHeight = isCompact ? 300 : 400;
  const chartWidth = isCompact ? 500 : 600;

  return (
    <div class={gridClasses}>
      {/* Real-time Churn Risk Gauge - Primary Position */}
      <div class={`${isCompact ? 'col-span-1' : 'col-span-1'} ${
        expandedChart.value === 'churn' ? 'lg:col-span-2 xl:col-span-3' : ''
      }`}>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Customer Churn Risk</h3>
            <button
              onClick={() => handleChartExpand('churn')}
              class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title={expandedChart.value === 'churn' ? 'Collapse' : 'Expand'}
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d={expandedChart.value === 'churn' ? "M20 12H4" : "M12 4v16m8-8H4"} />
              </svg>
            </button>
          </div>
          
          {metrics.value.hasChurnData ? (
            <D3ChurnGaugeRealtime
              tenantId="demo-tenant-123"
              initialData={getChurnData()}
              width={expandedChart.value === 'churn' ? 800 : chartWidth}
              height={expandedChart.value === 'churn' ? 500 : chartHeight}
              enableRealtimeUpdates={settings.value.enableRealtimeUpdates}
              animationDuration={settings.value.animationDuration}
              showRiskBreakdown={!isCompact}
            />
          ) : (
            <div class="flex items-center justify-center h-64 text-gray-500">
              <div class="text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <p>Loading churn data...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Revenue Forecasting */}
      <div class={`${isCompact ? 'col-span-1' : 'col-span-1'} ${
        expandedChart.value === 'revenue' ? 'lg:col-span-2 xl:col-span-3' : ''
      }`}>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Revenue Forecasting</h3>
            <button
              onClick={() => handleChartExpand('revenue')}
              class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d={expandedChart.value === 'revenue' ? "M20 12H4" : "M12 4v16m8-8H4"} />
              </svg>
            </button>
          </div>
          
          {metrics.value.hasRevenueData ? (
            <D3RevenueForecasting
              data={getRevenueData()}
              width={expandedChart.value === 'revenue' ? 900 : chartWidth + 100}
              height={expandedChart.value === 'revenue' ? 500 : chartHeight}
              showConfidenceInterval={showAdvanced}
              enableZoom={!isCompact}
              forecastStartDate="2024-07-01"
            />
          ) : (
            <div class="flex items-center justify-center h-64 text-gray-500">
              <div class="text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <p>Loading revenue forecast...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Cohort Retention Heatmap */}
      <div class={`${isCompact ? 'col-span-1' : 'lg:col-span-2'} ${
        expandedChart.value === 'cohort' ? 'lg:col-span-2 xl:col-span-3' : ''
      }`}>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Cohort Retention Analysis</h3>
            <button
              onClick={() => handleChartExpand('cohort')}
              class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d={expandedChart.value === 'cohort' ? "M20 12H4" : "M12 4v16m8-8H4"} />
              </svg>
            </button>
          </div>
          
          {metrics.value.hasCohortData ? (
            <D3CohortHeatmap
              data={getCohortData()}
              width={expandedChart.value === 'cohort' ? 1000 : (isCompact ? chartWidth : 800)}
              height={expandedChart.value === 'cohort' ? 600 : chartHeight + 50}
              showTooltips={!isCompact}
              enableInteraction={!isCompact}
            />
          ) : (
            <div class="flex items-center justify-center h-64 text-gray-500">
              <div class="text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <p>Loading cohort data...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* CLV Distribution */}
      <div class={`${isCompact ? 'col-span-1' : 'col-span-1'} ${
        expandedChart.value === 'clv' ? 'lg:col-span-2 xl:col-span-3' : ''
      }`}>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Customer Lifetime Value</h3>
            <button
              onClick={() => handleChartExpand('clv')}
              class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d={expandedChart.value === 'clv' ? "M20 12H4" : "M12 4v16m8-8H4"} />
              </svg>
            </button>
          </div>
          
          {metrics.value.hasCLVData ? (
            <D3CLVHistogram
              data={getCLVData().data}
              statistics={getCLVData().statistics}
              width={expandedChart.value === 'clv' ? 900 : chartWidth + 100}
              height={expandedChart.value === 'clv' ? 500 : chartHeight}
              showStatistics={showAdvanced && !isCompact}
              enableBrushing={!isCompact}
            />
          ) : (
            <div class="flex items-center justify-center h-64 text-gray-500">
              <div class="text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <p>Loading CLV data...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Conversion Funnel */}
      <div class={`${isCompact ? 'col-span-1' : 'col-span-1'} ${
        expandedChart.value === 'funnel' ? 'lg:col-span-2 xl:col-span-3' : ''
      }`}>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Conversion Funnel</h3>
            <button
              onClick={() => handleChartExpand('funnel')}
              class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d={expandedChart.value === 'funnel' ? "M20 12H4" : "M12 4v16m8-8H4"} />
              </svg>
            </button>
          </div>
          
          {metrics.value.hasFunnelData ? (
            <D3FunnelChart
              data={getFunnelData()}
              width={expandedChart.value === 'funnel' ? 800 : chartWidth}
              height={expandedChart.value === 'funnel' ? 600 : chartHeight + 50}
              showConversionRates={showAdvanced}
              showDropoffRates={showAdvanced}
              colorScheme="gradient"
            />
          ) : (
            <div class="flex items-center justify-center h-64 text-gray-500">
              <div class="text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <p>Loading funnel data...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Customer Journey Flow */}
      {(!isCompact || showAdvanced) && (
        <div class={`${isCompact ? 'col-span-1' : 'lg:col-span-2'} ${
          expandedChart.value === 'sankey' ? 'lg:col-span-2 xl:col-span-3' : ''
        }`}>
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Customer Journey Flow</h3>
              <button
                onClick={() => handleChartExpand('sankey')}
                class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d={expandedChart.value === 'sankey' ? "M20 12H4" : "M12 4v16m8-8H4"} />
                </svg>
              </button>
            </div>
            
            {metrics.value.hasSankeyData ? (
              <D3SankeyFlow
                data={getSankeyData()!}
                width={expandedChart.value === 'sankey' ? 1000 : (isCompact ? chartWidth : 800)}
                height={expandedChart.value === 'sankey' ? 600 : chartHeight + 50}
                showLabels={!isCompact}
                enableInteraction={!isCompact}
                colorScheme="stage"
              />
            ) : (
              <div class="flex items-center justify-center h-64 text-gray-500">
                <div class="text-center">
                  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p>Loading journey data...</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Performance Summary Card */}
      {showAdvanced && (
        <div class="col-span-1">
          <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg border border-blue-200 p-4">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">Performance Summary</h3>
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-sm text-blue-700">Revenue per User</span>
                <span class="font-semibold text-blue-900">
                  ${Math.round(metrics.value.revenuePerUser)}
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-blue-700">Active Charts</span>
                <span class="font-semibold text-blue-900">
                  {[
                    metrics.value.hasChurnData,
                    metrics.value.hasCohortData,
                    metrics.value.hasCLVData,
                    metrics.value.hasFunnelData,
                    metrics.value.hasRevenueData,
                    metrics.value.hasSankeyData
                  ].filter(Boolean).length}/6
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-blue-700">Last Updated</span>
                <span class="text-xs text-blue-600">
                  {metrics.value.lastUpdated 
                    ? new Date(metrics.value.lastUpdated).toLocaleTimeString()
                    : 'Never'
                  }
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
