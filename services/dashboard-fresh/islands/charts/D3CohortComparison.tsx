// D3.js Cohort Comparison Visualization Component
// Week 17-18 Implementation: Multi-line chart comparing retention curves across cohorts

import { useEffect, useRef } from "preact/hooks";
import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import * as d3 from "d3";
import { 
  ChartDimensions,
  DEFAULT_CHART_CONFIG,
  getInnerDimensions,
  formatNumber,
  formatDate,
  createTimeScale,
  createLinearScale,
  createColorScale,
  animateEntrance,
  addHoverEffects
} from "../../utils/d3-base.ts";

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface CohortComparisonData {
  cohortId: string;
  cohortDate: string;
  retentionCurve: {
    period: number;
    retentionRate: number;
    customerCount: number;
  }[];
}

export interface D3CohortComparisonProps {
  data: CohortComparisonData[];
  width?: number;
  height?: number;
  title?: string;
  className?: string;
  showTrendLines?: boolean;
  maxCohorts?: number;
  onLineClick?: (cohort: CohortComparisonData, event: MouseEvent) => void;
  onPointHover?: (data: { cohort: CohortComparisonData; point: any } | null) => void;
}

// =====================================================
// COHORT COMPARISON CHART CLASS
// =====================================================

class CohortComparisonChart {
  private svg: d3.Selection<SVGSVGElement, unknown, null, undefined>;
  private container: d3.Selection<HTMLDivElement, unknown, null, undefined>;
  private tooltip: d3.Selection<HTMLDivElement, unknown, null, undefined>;
  private dimensions: ChartDimensions;
  private data: CohortComparisonData[];
  private colorScale: d3.ScaleOrdinal<string, string>;
  private xScale: d3.ScaleLinear<number, number>;
  private yScale: d3.ScaleLinear<number, number>;
  private line: d3.Line<any>;
  private showTrendLines: boolean;

  constructor(
    svgElement: SVGSVGElement,
    containerElement: HTMLDivElement,
    dimensions: ChartDimensions,
    data: CohortComparisonData[],
    options: { showTrendLines?: boolean } = {}
  ) {
    this.svg = d3.select(svgElement);
    this.container = d3.select(containerElement);
    this.dimensions = dimensions;
    this.data = data;
    this.showTrendLines = options.showTrendLines || false;
    
    // Create tooltip
    this.tooltip = this.container
      .append("div")
      .attr("class", "cohort-comparison-tooltip")
      .style("position", "absolute")
      .style("visibility", "hidden")
      .style("background-color", "rgba(0, 0, 0, 0.9)")
      .style("color", "white")
      .style("padding", "12px")
      .style("border-radius", "6px")
      .style("font-size", "12px")
      .style("pointer-events", "none")
      .style("z-index", "1000")
      .style("box-shadow", "0 4px 6px rgba(0, 0, 0, 0.1)");

    this.initialize();
  }

  private initialize(): void {
    const { width, height } = this.dimensions;
    
    this.svg
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", `0 0 ${width} ${height}`)
      .style("max-width", "100%")
      .style("height", "auto");

    this.setupScales();
    this.render();
  }

  private setupScales(): void {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);

    // Get all periods and retention rates
    const allPeriods = this.data.flatMap(d => d.retentionCurve.map(p => p.period));
    const allRetentionRates = this.data.flatMap(d => d.retentionCurve.map(p => p.retentionRate));

    // Create scales
    this.xScale = d3.scaleLinear()
      .domain(d3.extent(allPeriods) as [number, number])
      .range([0, innerWidth])
      .nice();

    this.yScale = d3.scaleLinear()
      .domain([0, d3.max(allRetentionRates) || 100])
      .range([innerHeight, 0])
      .nice();

    // Color scale for different cohorts
    const cohortIds = this.data.map(d => d.cohortId);
    this.colorScale = createColorScale(cohortIds);

    // Line generator
    this.line = d3.line()
      .x(d => this.xScale(d.period))
      .y(d => this.yScale(d.retentionRate))
      .curve(d3.curveMonotoneX);
  }

  public render(): void {
    this.svg.selectAll("*").remove();

    const { margin } = this.dimensions;
    const g = this.svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    this.renderAxes(g);
    this.renderGridLines(g);
    this.renderRetentionLines(g);
    this.renderDataPoints(g);
    if (this.showTrendLines) {
      this.renderTrendLines(g);
    }
    this.renderLegend(g);
  }

  private renderAxes(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);

    // X-axis
    const xAxis = d3.axisBottom(this.xScale)
      .tickFormat(d => `Period ${d}`);

    g.append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0,${innerHeight})`)
      .call(xAxis)
      .selectAll("text")
      .style("font-size", "11px")
      .style("fill", "#6b7280");

    // Y-axis
    const yAxis = d3.axisLeft(this.yScale)
      .tickFormat(d => formatNumber(d, 'percentage'));

    g.append("g")
      .attr("class", "y-axis")
      .call(yAxis)
      .selectAll("text")
      .style("font-size", "11px")
      .style("fill", "#6b7280");

    // Axis labels
    g.append("text")
      .attr("class", "x-axis-label")
      .attr("x", innerWidth / 2)
      .attr("y", innerHeight + 35)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", "#374151")
      .text("Time Period");

    g.append("text")
      .attr("class", "y-axis-label")
      .attr("transform", "rotate(-90)")
      .attr("x", -innerHeight / 2)
      .attr("y", -35)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", "#374151")
      .text("Retention Rate");
  }

  private renderGridLines(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);

    // Horizontal grid lines
    g.selectAll(".grid-line-horizontal")
      .data(this.yScale.ticks())
      .enter()
      .append("line")
      .attr("class", "grid-line-horizontal")
      .attr("x1", 0)
      .attr("x2", innerWidth)
      .attr("y1", d => this.yScale(d))
      .attr("y2", d => this.yScale(d))
      .style("stroke", "#f3f4f6")
      .style("stroke-width", 1);

    // Vertical grid lines
    g.selectAll(".grid-line-vertical")
      .data(this.xScale.ticks())
      .enter()
      .append("line")
      .attr("class", "grid-line-vertical")
      .attr("x1", d => this.xScale(d))
      .attr("x2", d => this.xScale(d))
      .attr("y1", 0)
      .attr("y2", innerHeight)
      .style("stroke", "#f3f4f6")
      .style("stroke-width", 1);
  }

  private renderRetentionLines(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const lines = g.selectAll(".retention-line")
      .data(this.data)
      .enter()
      .append("path")
      .attr("class", "retention-line")
      .attr("d", d => this.line(d.retentionCurve))
      .style("fill", "none")
      .style("stroke", d => this.colorScale(d.cohortId))
      .style("stroke-width", 2.5)
      .style("cursor", "pointer");

    // Add hover effects
    addHoverEffects(
      lines,
      (event, d) => {
        d3.select(event.currentTarget).style("stroke-width", 4);
        this.highlightCohort(d.cohortId, true);
      },
      (event, d) => {
        d3.select(event.currentTarget).style("stroke-width", 2.5);
        this.highlightCohort(d.cohortId, false);
      }
    );

    // Add click handlers
    lines.on("click", (event, d) => {
      const customEvent = new CustomEvent("lineClick", { detail: { cohort: d, event } });
      this.container.node()?.dispatchEvent(customEvent);
    });

    // Animate entrance
    animateEntrance(lines);
  }

  private renderDataPoints(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    this.data.forEach(cohort => {
      const points = g.selectAll(`.data-point-${cohort.cohortId}`)
        .data(cohort.retentionCurve)
        .enter()
        .append("circle")
        .attr("class", `data-point data-point-${cohort.cohortId}`)
        .attr("cx", d => this.xScale(d.period))
        .attr("cy", d => this.yScale(d.retentionRate))
        .attr("r", 4)
        .style("fill", this.colorScale(cohort.cohortId))
        .style("stroke", "#fff")
        .style("stroke-width", 2)
        .style("cursor", "pointer");

      // Add hover effects
      addHoverEffects(
        points,
        (event, d) => this.showTooltip(cohort, d, event),
        () => this.hideTooltip()
      );

      // Animate entrance
      animateEntrance(points, cohort.retentionCurve.map((_, i) => i * 50));
    });
  }

  private renderTrendLines(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    this.data.forEach(cohort => {
      // Calculate linear regression for trend line
      const points = cohort.retentionCurve.map(d => [d.period, d.retentionRate]);
      const regression = this.calculateLinearRegression(points);
      
      if (regression) {
        const trendLine = d3.line()
          .x(d => this.xScale(d[0]))
          .y(d => this.yScale(d[1]));

        const xExtent = d3.extent(cohort.retentionCurve, d => d.period) as [number, number];
        const trendData = [
          [xExtent[0], regression.slope * xExtent[0] + regression.intercept],
          [xExtent[1], regression.slope * xExtent[1] + regression.intercept]
        ];

        g.append("path")
          .attr("class", `trend-line trend-line-${cohort.cohortId}`)
          .attr("d", trendLine(trendData))
          .style("fill", "none")
          .style("stroke", this.colorScale(cohort.cohortId))
          .style("stroke-width", 1)
          .style("stroke-dasharray", "5,5")
          .style("opacity", 0.6);
      }
    });
  }

  private renderLegend(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const { width: innerWidth } = getInnerDimensions(this.dimensions);
    const legendX = innerWidth - 150;
    const legendY = 20;

    const legend = g.append("g")
      .attr("class", "legend")
      .attr("transform", `translate(${legendX},${legendY})`);

    const legendItems = legend.selectAll(".legend-item")
      .data(this.data.slice(0, 8)) // Limit to 8 items for space
      .enter()
      .append("g")
      .attr("class", "legend-item")
      .attr("transform", (d, i) => `translate(0,${i * 20})`)
      .style("cursor", "pointer");

    // Legend color squares
    legendItems.append("rect")
      .attr("width", 12)
      .attr("height", 12)
      .style("fill", d => this.colorScale(d.cohortId))
      .style("stroke", "#fff")
      .style("stroke-width", 1);

    // Legend labels
    legendItems.append("text")
      .attr("x", 18)
      .attr("y", 6)
      .attr("dy", "0.35em")
      .style("font-size", "11px")
      .style("fill", "#374151")
      .text(d => formatDate(d.cohortDate, 'short'));

    // Legend interactions
    legendItems.on("click", (event, d) => {
      this.toggleCohortVisibility(d.cohortId);
    });

    addHoverEffects(
      legendItems,
      (event, d) => this.highlightCohort(d.cohortId, true),
      (event, d) => this.highlightCohort(d.cohortId, false)
    );
  }

  private calculateLinearRegression(points: number[][]): { slope: number; intercept: number } | null {
    if (points.length < 2) return null;

    const n = points.length;
    const sumX = points.reduce((sum, p) => sum + p[0], 0);
    const sumY = points.reduce((sum, p) => sum + p[1], 0);
    const sumXY = points.reduce((sum, p) => sum + p[0] * p[1], 0);
    const sumXX = points.reduce((sum, p) => sum + p[0] * p[0], 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    return { slope, intercept };
  }

  private highlightCohort(cohortId: string, highlight: boolean): void {
    const opacity = highlight ? 0.3 : 1;
    
    this.svg.selectAll(".retention-line")
      .style("opacity", d => d.cohortId === cohortId ? 1 : opacity);
    
    this.svg.selectAll(".data-point")
      .style("opacity", d => {
        const point = d3.select(this as any);
        return point.classed(`data-point-${cohortId}`) ? 1 : opacity;
      });
  }

  private toggleCohortVisibility(cohortId: string): void {
    const line = this.svg.select(`.retention-line[data-cohort="${cohortId}"]`);
    const isVisible = line.style("opacity") !== "0";
    
    line.style("opacity", isVisible ? 0 : 1);
    this.svg.selectAll(`.data-point-${cohortId}`)
      .style("opacity", isVisible ? 0 : 1);
  }

  private showTooltip(cohort: CohortComparisonData, point: any, event: MouseEvent): void {
    const content = `
      <div class="font-semibold mb-1">${formatDate(cohort.cohortDate, 'short')}</div>
      <div>Period: ${point.period}</div>
      <div>Retention: ${formatNumber(point.retentionRate, 'percentage')}</div>
      <div>Customers: ${formatNumber(point.customerCount, 'integer')}</div>
    `;

    this.tooltip
      .style("visibility", "visible")
      .html(content)
      .style("left", `${event.pageX + 10}px`)
      .style("top", `${event.pageY - 10}px`);
  }

  private hideTooltip(): void {
    this.tooltip.style("visibility", "hidden");
  }

  public updateData(newData: CohortComparisonData[]): void {
    this.data = newData;
    this.setupScales();
    this.render();
  }

  public updateDimensions(newDimensions: ChartDimensions): void {
    this.dimensions = newDimensions;
    this.svg
      .attr("width", newDimensions.width)
      .attr("height", newDimensions.height)
      .attr("viewBox", `0 0 ${newDimensions.width} ${newDimensions.height}`);
    this.setupScales();
    this.render();
  }

  public destroy(): void {
    this.tooltip.remove();
    this.svg.selectAll("*").remove();
  }
}

// =====================================================
// REACT COMPONENT
// =====================================================

export default function D3CohortComparison({
  data,
  width = 800,
  height = 400,
  title = "Cohort Retention Comparison",
  className = "",
  showTrendLines = false,
  maxCohorts = 10,
  onLineClick,
  onPointHover
}: D3CohortComparisonProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<CohortComparisonChart | null>(null);
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);

  // Limit data to maxCohorts for performance
  const limitedData = data.slice(0, maxCohorts);

  useEffect(() => {
    if (!IS_BROWSER || !svgRef.current || !containerRef.current || !limitedData.length) {
      return;
    }

    try {
      loading.value = true;
      error.value = null;

      const dimensions: ChartDimensions = {
        width,
        height,
        margin: { top: 20, right: 180, bottom: 60, left: 60 }
      };

      chartRef.current = new CohortComparisonChart(
        svgRef.current,
        containerRef.current,
        dimensions,
        limitedData,
        { showTrendLines }
      );

      loading.value = false;
    } catch (err) {
      console.error("Error creating cohort comparison chart:", err);
      error.value = err instanceof Error ? err.message : "Unknown error";
      loading.value = false;
    }

    return () => {
      chartRef.current?.destroy();
    };
  }, [limitedData, width, height, showTrendLines]);

  // Handle line click events
  useEffect(() => {
    if (!containerRef.current || !onLineClick) return;

    const handleLineClick = (event: CustomEvent) => {
      const { cohort, event: mouseEvent } = event.detail;
      onLineClick(cohort, mouseEvent);
    };

    containerRef.current.addEventListener("lineClick", handleLineClick as EventListener);
    
    return () => {
      containerRef.current?.removeEventListener("lineClick", handleLineClick as EventListener);
    };
  }, [onLineClick]);

  if (loading.value) {
    return (
      <div className={`cohort-comparison-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading comparison chart...</span>
        </div>
      </div>
    );
  }

  if (error.value) {
    return (
      <div className={`cohort-comparison-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-red-50 rounded-lg border border-red-200">
          <div className="text-center">
            <p className="text-red-700 font-medium">Error Loading Chart</p>
            <p className="text-red-600 text-sm mt-1">{error.value}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!limitedData.length) {
    return (
      <div className={`cohort-comparison-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-200">
          <div className="text-center">
            <p className="text-gray-600 font-medium">No Cohort Data Available</p>
            <p className="text-gray-500 text-sm mt-1">Comparison chart will appear when data is loaded</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`cohort-comparison-container relative ${className}`} ref={containerRef}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>
      )}
      <svg
        ref={svgRef}
        className="border border-gray-200 rounded-lg bg-white shadow-sm"
        style={{ maxWidth: "100%", height: "auto" }}
      />
      {data.length > maxCohorts && (
        <p className="text-sm text-gray-500 mt-2 text-center">
          Showing {maxCohorts} of {data.length} cohorts for performance
        </p>
      )}
    </div>
  );
}
