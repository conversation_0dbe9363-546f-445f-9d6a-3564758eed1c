import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { JSX } from "preact";

// Enhanced Analytics Dashboard Island for Phase 1 Implementation
// Integrates with the new enhanced analytics API endpoints

interface DashboardMetrics {
  overview: {
    totalRevenue: number;
    revenueChange: number;
    totalOrders: number;
    ordersChange: number;
    totalCustomers: number;
    customersChange: number;
    avgOrderValue: number;
    conversionRate: number;
    engagementRate: number;
  };
  activity: {
    totalEvents: number;
    totalSessions: number;
    totalClicks: number;
    uniqueClicks: number;
    activeLinks: number;
  };
  socialMedia: {
    totalImpressions: number;
    totalEngagement: number;
    totalReach: number;
  };
  period: {
    from: string;
    to: string;
    range: string;
  };
  generatedAt: string;
}

interface RealTimeData {
  hourlyData: Array<{
    hour: string;
    revenue: number;
    orders: number;
    customers: number;
    sessions: number;
  }>;
  summary: {
    last24Hours: {
      revenue: number;
      orders: number;
      customers: number;
    };
  };
  generatedAt: string;
}

export default function EnhancedAnalyticsDashboard() {
  const timeRange = useSignal("30d");
  const dashboardData = useSignal<DashboardMetrics | null>(null);
  const realTimeData = useSignal<RealTimeData | null>(null);
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);

  const timeRangeOptions = useComputed(() => [
    { value: "7d", label: "Last 7 days" },
    { value: "30d", label: "Last 30 days" },
    { value: "90d", label: "Last 90 days" },
    { value: "1y", label: "Last year" },
  ]);

  const fetchDashboardData = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch(`/api/analytics/enhanced/dashboard?range=${timeRange.value}`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-ID': getTenantId(),
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      dashboardData.value = result.data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An error occurred';
    } finally {
      loading.value = false;
    }
  };

  const fetchRealTimeData = async () => {
    try {
      const response = await fetch('/api/analytics/enhanced/realtime/revenue', {
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-ID': getTenantId(),
        },
      });
      
      if (response.ok) {
        const result = await response.json();
        realTimeData.value = result.data;
      }
    } catch (err) {
      console.error('Failed to fetch real-time data:', err);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    
    // Set up real-time updates
    const realTimeInterval = setInterval(fetchRealTimeData, 60000); // Every minute
    fetchRealTimeData(); // Initial fetch
    
    return () => {
      clearInterval(realTimeInterval);
    };
  }, [timeRange.value]);

  if (loading.value) {
    return <DashboardSkeleton />;
  }

  if (error.value) {
    return <ErrorDisplay error={error.value} onRetry={fetchDashboardData} />;
  }

  return (
    <div class="enhanced-analytics-dashboard min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardHeader 
        timeRange={timeRange.value}
        timeRangeOptions={timeRangeOptions.value}
        onTimeRangeChange={(range) => timeRange.value = range}
      />
      
      <div class="container mx-auto px-4 py-6">
        {/* Key Metrics Overview */}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <MetricCard
            title="Total Revenue"
            value={formatCurrency(dashboardData.value?.overview.totalRevenue || 0)}
            change={dashboardData.value?.overview.revenueChange || 0}
            icon="💰"
            trend="revenue"
          />
          <MetricCard
            title="Total Orders"
            value={formatNumber(dashboardData.value?.overview.totalOrders || 0)}
            change={dashboardData.value?.overview.ordersChange || 0}
            icon="📦"
            trend="orders"
          />
          <MetricCard
            title="Customers"
            value={formatNumber(dashboardData.value?.overview.totalCustomers || 0)}
            change={dashboardData.value?.overview.customersChange || 0}
            icon="👥"
            trend="customers"
          />
          <MetricCard
            title="Conversion Rate"
            value={`${(dashboardData.value?.overview.conversionRate || 0).toFixed(2)}%`}
            change={0} // TODO: Add conversion rate change calculation
            icon="📈"
            trend="conversion"
          />
        </div>

        {/* Real-time Revenue Section */}
        {realTimeData.value && (
          <div class="mb-8">
            <RealTimeRevenueCard data={realTimeData.value} />
          </div>
        )}

        {/* Secondary Metrics */}
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <SecondaryMetricCard
            title="Average Order Value"
            value={formatCurrency(dashboardData.value?.overview.avgOrderValue || 0)}
            icon="💳"
          />
          <SecondaryMetricCard
            title="Engagement Rate"
            value={`${(dashboardData.value?.overview.engagementRate || 0).toFixed(2)}%`}
            icon="❤️"
          />
          <SecondaryMetricCard
            title="Active Links"
            value={formatNumber(dashboardData.value?.activity.activeLinks || 0)}
            icon="🔗"
          />
        </div>

        {/* Activity Overview */}
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
          <ActivityOverviewCard data={dashboardData.value?.activity} />
          <SocialMediaOverviewCard data={dashboardData.value?.socialMedia} />
        </div>

        {/* Performance Indicators */}
        <div class="mb-8">
          <PerformanceIndicators />
        </div>
      </div>
    </div>
  );
}

// Component: Dashboard Header
interface DashboardHeaderProps {
  timeRange: string;
  timeRangeOptions: Array<{ value: string; label: string }>;
  onTimeRangeChange: (range: string) => void;
}

function DashboardHeader({ timeRange, timeRangeOptions, onTimeRangeChange }: DashboardHeaderProps) {
  return (
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div class="container mx-auto px-4 py-4">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              Enhanced Analytics Dashboard
            </h1>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Real-time insights and performance metrics
            </p>
          </div>
          
          <div class="mt-4 sm:mt-0">
            <select
              value={timeRange}
              onChange={(e) => onTimeRangeChange((e.target as HTMLSelectElement).value)}
              class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {timeRangeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
}

// Component: Metric Card
interface MetricCardProps {
  title: string;
  value: string;
  change?: number;
  icon?: string;
  trend?: string;
}

function MetricCard({ title, value, change, icon, trend }: MetricCardProps) {
  const changeColor = change && change > 0 ? "text-green-600" : change && change < 0 ? "text-red-600" : "text-gray-500";
  const changeIcon = change && change > 0 ? "↗️" : change && change < 0 ? "↘️" : "➡️";

  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">{value}</p>
          {change !== undefined && (
            <p class={`text-sm mt-1 ${changeColor}`}>
              <span class="mr-1">{changeIcon}</span>
              {Math.abs(change).toFixed(1)}% vs previous period
            </p>
          )}
        </div>
        {icon && (
          <div class="text-3xl opacity-80 ml-4">{icon}</div>
        )}
      </div>
    </div>
  );
}

// Component: Real-time Revenue Card
interface RealTimeRevenueCardProps {
  data: RealTimeData;
}

function RealTimeRevenueCard({ data }: RealTimeRevenueCardProps) {
  return (
    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg p-6 text-white">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">Real-time Revenue (Last 24 Hours)</h3>
        <div class="flex items-center text-sm opacity-90">
          <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
          Live
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <p class="text-sm opacity-90">Revenue</p>
          <p class="text-2xl font-bold">{formatCurrency(data.summary.last24Hours.revenue)}</p>
        </div>
        <div>
          <p class="text-sm opacity-90">Orders</p>
          <p class="text-2xl font-bold">{formatNumber(data.summary.last24Hours.orders)}</p>
        </div>
        <div>
          <p class="text-sm opacity-90">Customers</p>
          <p class="text-2xl font-bold">{formatNumber(data.summary.last24Hours.customers)}</p>
        </div>
      </div>
    </div>
  );
}

// Component: Dashboard Skeleton
function DashboardSkeleton() {
  return (
    <div class="animate-pulse">
      <div class="bg-gray-200 dark:bg-gray-700 h-20 mb-6"></div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} class="bg-gray-200 dark:bg-gray-700 rounded-lg h-24"></div>
        ))}
      </div>
      <div class="bg-gray-200 dark:bg-gray-700 rounded-lg h-32 mb-8"></div>
      <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
        <div class="bg-gray-200 dark:bg-gray-700 rounded-lg h-64"></div>
        <div class="bg-gray-200 dark:bg-gray-700 rounded-lg h-64"></div>
      </div>
    </div>
  );
}

// Component: Error Display
function ErrorDisplay({ error, onRetry }: { error: string; onRetry: () => void }) {
  return (
    <div class="flex flex-col items-center justify-center min-h-64 text-center">
      <div class="text-red-500 text-6xl mb-4">⚠️</div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        Something went wrong
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
      <button
        onClick={onRetry}
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Try Again
      </button>
    </div>
  );
}

// Utility Functions
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

function formatNumber(num: number): string {
  return new Intl.NumberFormat('en-US').format(num);
}

function getTenantId(): string {
  // In a real implementation, this would come from authentication context
  return '00000000-0000-0000-0000-000000000001';
}

// Additional component stubs (to be implemented)
function SecondaryMetricCard({ title, value, icon }: { title: string; value: string; icon: string }) {
  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <div class="flex items-center">
        <span class="text-2xl mr-3">{icon}</span>
        <div>
          <p class="text-sm text-gray-600 dark:text-gray-400">{title}</p>
          <p class="text-lg font-semibold text-gray-900 dark:text-white">{value}</p>
        </div>
      </div>
    </div>
  );
}

function ActivityOverviewCard({ data }: { data?: DashboardMetrics['activity'] }) {
  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Activity Overview</h3>
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-gray-600 dark:text-gray-400">Total Events</span>
          <span class="font-medium text-gray-900 dark:text-white">{formatNumber(data?.totalEvents || 0)}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-600 dark:text-gray-400">Sessions</span>
          <span class="font-medium text-gray-900 dark:text-white">{formatNumber(data?.totalSessions || 0)}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-600 dark:text-gray-400">Link Clicks</span>
          <span class="font-medium text-gray-900 dark:text-white">{formatNumber(data?.totalClicks || 0)}</span>
        </div>
      </div>
    </div>
  );
}

function SocialMediaOverviewCard({ data }: { data?: DashboardMetrics['socialMedia'] }) {
  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Social Media</h3>
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-gray-600 dark:text-gray-400">Impressions</span>
          <span class="font-medium text-gray-900 dark:text-white">{formatNumber(data?.totalImpressions || 0)}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-600 dark:text-gray-400">Engagement</span>
          <span class="font-medium text-gray-900 dark:text-white">{formatNumber(data?.totalEngagement || 0)}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-600 dark:text-gray-400">Reach</span>
          <span class="font-medium text-gray-900 dark:text-white">{formatNumber(data?.totalReach || 0)}</span>
        </div>
      </div>
    </div>
  );
}

function PerformanceIndicators() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Indicators</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">85ms</div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Avg Query Time</div>
          <div className="text-xs text-green-600">Target: &lt;100ms</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">8.5K</div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Events/Second</div>
          <div className="text-xs text-blue-600">Target: 10K+</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">72%</div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Compression</div>
          <div className="text-xs text-purple-600">Target: 70%</div>
        </div>
      </div>
    </div>
  );
}
