import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { IS_BROWSER } from "$fresh/runtime.ts";
import { LoadingSpinner } from "../../components/ui/LoadingSpinner.tsx";

interface MetricsData {
  totalRevenue: number;
  activeUsers: number;
  conversionRate: number;
  totalLinks: number;
  lastUpdated?: string;
}

interface RealtimeMetricsProps {
  initialData: MetricsData;
}

export default function RealtimeMetrics({ initialData }: RealtimeMetricsProps) {
  const metrics = useSignal<MetricsData>(initialData);
  const isConnected = useSignal(false);
  const isLoading = useSignal(false);
  const error = useSignal<string | null>(null);

  // Computed values for formatting with null checks
  const formattedRevenue = useComputed(() => {
    const revenue = metrics.value?.totalRevenue ?? 0;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(revenue);
  });

  const formattedUsers = useComputed(() => {
    const users = metrics.value?.activeUsers ?? 0;
    return users.toLocaleString();
  });

  const formattedConversionRate = useComputed(() => {
    const rate = metrics.value?.conversionRate ?? 0;
    return `${rate.toFixed(2)}%`;
  });

  const formattedLinks = useComputed(() => {
    const links = metrics.value?.totalLinks ?? 0;
    return links.toLocaleString();
  });

  // Manual refresh function
  const refreshMetrics = async () => {
    if (!IS_BROWSER) return;
    
    isLoading.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/dashboard/metrics');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        metrics.value = {
          ...result.data,
          lastUpdated: new Date().toISOString()
        };
      } else {
        throw new Error(result.error || 'Failed to fetch metrics');
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      console.error('Fetch metrics error:', err);
    } finally {
      isLoading.value = false;
    }
  };

  // Setup real-time updates via Server-Sent Events
  useEffect(() => {
    if (!IS_BROWSER) return;

    const eventSource = new EventSource('/api/realtime/metrics');

    eventSource.onopen = () => {
      isConnected.value = true;
      error.value = null;
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        // Handle different message types
        if (data.type === 'connected') {
          console.log('SSE connected:', data.message);
          return;
        }

        if (data.type === 'metrics_update') {
          // Extract only the metrics we need and ensure they exist
          const newMetrics: MetricsData = {
            totalRevenue: data.totalRevenue || 0,
            activeUsers: data.activeUsers || 0,
            conversionRate: parseFloat(data.conversionRate) || 0,
            totalLinks: data.totalLinks || 0,
            lastUpdated: new Date().toISOString()
          };

          metrics.value = newMetrics;
        }
      } catch (err) {
        console.error('Failed to parse SSE data:', err);
        error.value = 'Failed to process real-time data';
      }
    };

    eventSource.onerror = () => {
      isConnected.value = false;
      error.value = 'Connection lost. Trying to reconnect...';
    };

    // Cleanup on unmount
    return () => {
      eventSource.close();
    };
  }, []);

  // Auto-refresh fallback every 30 seconds if SSE is not connected
  useEffect(() => {
    if (!IS_BROWSER) return;

    const interval = setInterval(() => {
      if (!isConnected.value) {
        refreshMetrics();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div class="bg-white rounded-lg shadow-soft p-6">
      {/* Header */}
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-lg font-semibold text-gray-900">Live Metrics</h3>
        <div class="flex items-center space-x-3">
          {/* Connection Status */}
          <div class="flex items-center space-x-2">
            <div class={`w-2 h-2 rounded-full ${isConnected.value ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span class="text-xs text-gray-500">
              {isConnected.value ? 'Live' : 'Offline'}
            </span>
          </div>
          
          {/* Refresh Button */}
          <button
            type="button"
            onClick={refreshMetrics}
            disabled={isLoading.value}
            class={`flex items-center space-x-1 px-3 py-1 rounded text-xs transition-colors ${
              isLoading.value
                ? 'bg-gray-300 cursor-not-allowed text-gray-500'
                : 'bg-primary-500 hover:bg-primary-600 text-white'
            }`}
          >
            {isLoading.value && <LoadingSpinner size="sm" variant="white" />}
            <span>{isLoading.value ? 'Updating...' : 'Refresh'}</span>
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error.value && (
        <div class="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
          {error.value}
        </div>
      )}

      {/* Metrics Grid */}
      <div class="grid grid-cols-2 gap-4">
        {/* Revenue */}
        <div class="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-green-800">Revenue</p>
              <p class="text-2xl font-bold text-green-900">{formattedRevenue.value}</p>
            </div>
            <div class="w-10 h-10 bg-green-200 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
        </div>

        {/* Active Users */}
        <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-blue-800">Active Users</p>
              <p class="text-2xl font-bold text-blue-900">{formattedUsers.value}</p>
            </div>
            <div class="w-10 h-10 bg-blue-200 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Conversion Rate */}
        <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-purple-800">Conversion Rate</p>
              <p class="text-2xl font-bold text-purple-900">{formattedConversionRate.value}</p>
            </div>
            <div class="w-10 h-10 bg-purple-200 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Total Links */}
        <div class="bg-gradient-to-r from-orange-50 to-orange-100 p-4 rounded-lg">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-orange-800">Total Links</p>
              <p class="text-2xl font-bold text-orange-900">{formattedLinks.value}</p>
            </div>
            <div class="w-10 h-10 bg-orange-200 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Last Updated */}
      {metrics.value.lastUpdated && (
        <div class="mt-4 text-xs text-gray-500 text-center">
          Last updated: {new Date(metrics.value.lastUpdated).toLocaleTimeString()}
        </div>
      )}
    </div>
  );
}
