// Analytics Data Service for D3.js Visualizations
// Week 17-18 Implementation: Server-side data fetching for enhanced analytics

// Simple logger fallback
const logger = {
  error: (message: string, error?: unknown) => {
    console.error(message, error);
  },
  warn: (message: string, data?: unknown) => {
    console.warn(message, data);
  },
  info: (message: string, data?: unknown) => {
    console.info(message, data);
  },
};

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface AnalyticsDataOptions {
  tenantId: string;
  dateFrom?: string;
  dateTo?: string;
  granularity?: 'daily' | 'weekly' | 'monthly';
  includeProjections?: boolean;
}

export interface CohortVisualizationData {
  heatmapData: {
    cohortId: string;
    cohortDate: string;
    period: number;
    retentionRate: number;
    customerCount: number;
  }[];
  retentionCurves: {
    [cohortId: string]: {
      period: number;
      retentionRate: number;
      customerCount: number;
    }[];
  };
  overview: {
    totalCohorts: number;
    avgRetentionRate: number;
    bestPerformingCohort: string;
    totalCustomers: number;
  };
}

export interface CLVVisualizationData {
  distribution: {
    clvRange: string;
    customerCount: number;
    totalClv: number;
    percentage: number;
  }[];
  segments: {
    segmentName: string;
    customerCount: number;
    avgClv: number;
    totalClv: number;
    color: string;
  }[];
  trends: {
    date: string;
    avgClv: number;
    medianClv: number;
    totalClv: number;
  }[];
}

// Attribution Analysis Data Interfaces
export interface AttributionVisualizationData {
  modelComparison: {
    modelName: string;
    modelType: 'first_touch' | 'last_touch' | 'linear' | 'time_decay' | 'position_based';
    totalAttributedRevenue: number;
    totalConversions: number;
    avgOrderValue: number;
    topChannels: {
      channel: string;
      attributedRevenue: number;
      attributionPercentage: number;
      conversions: number;
    }[];
  }[];
  customerJourneys: {
    journeyId: string;
    customerId: string;
    touchpoints: {
      timestamp: string;
      channel: string;
      source: string;
      medium: string;
      campaign?: string;
      attributionWeight: number;
      position: number;
    }[];
    conversionValue: number;
    conversionTimestamp: string;
    pathLength: number;
    timeToConversion: number; // in hours
  }[];
  channelPerformance: {
    channel: string;
    source: string;
    medium: string;
    totalClicks: number;
    totalConversions: number;
    totalRevenue: number;
    conversionRate: number;
    attributionWeights: {
      firstTouch: number;
      lastTouch: number;
      linear: number;
      timeDecay: number;
      positionBased: number;
    };
    avgTimeToConversion: number;
    avgOrderValue: number;
  }[];
  overview: {
    totalAttributedRevenue: number;
    totalConversions: number;
    avgPathLength: number;
    topPerformingChannel: string;
    totalChannels: number;
    avgTimeToConversion: number;
  };
}

// Real-time Analytics Data Interfaces
export interface RealtimeVisualizationData {
  metrics: {
    activeUsers: number;
    pageViews: number;
    conversions: number;
    revenue: number;
    bounceRate: number;
    avgSessionDuration: number;
    trends: {
      activeUsers: 'up' | 'down' | 'stable';
      pageViews: 'up' | 'down' | 'stable';
      conversions: 'up' | 'down' | 'stable';
      revenue: 'up' | 'down' | 'stable';
    };
  };
  eventStream: {
    eventId: string;
    timestamp: string;
    eventType: 'page_view' | 'purchase' | 'add_to_cart' | 'signup' | 'click';
    userId?: string;
    sessionId: string;
    page?: string;
    product?: string;
    value?: number;
    country?: string;
    city?: string;
    device?: string;
    source?: string;
  }[];
  funnelData: {
    stage: string;
    stageOrder: number;
    visitors: number;
    conversions: number;
    conversionRate: number;
    dropOffRate: number;
    avgTimeInStage: number;
  }[];
  geographyData: {
    country: string;
    countryCode: string;
    visitors: number;
    pageViews: number;
    conversions: number;
    revenue: number;
    latitude: number;
    longitude: number;
  }[];
  topPages: {
    page: string;
    pageViews: number;
    uniqueVisitors: number;
    avgTimeOnPage: number;
    bounceRate: number;
  }[];
  topProducts: {
    productId: string;
    productName: string;
    views: number;
    purchases: number;
    revenue: number;
    conversionRate: number;
  }[];
  overview: {
    totalActiveUsers: number;
    totalPageViews: number;
    totalConversions: number;
    totalRevenue: number;
    peakActiveUsers: number;
    avgSessionDuration: number;
  };
}

export interface FunnelVisualizationData {
  steps: {
    stepId: string;
    stepName: string;
    stepOrder: number;
    totalUsers: number;
    convertedUsers: number;
    conversionRate: number;
    dropoffRate: number;
  }[];
  flows: {
    source: string;
    target: string;
    value: number;
  }[];
  overview: {
    totalFunnels: number;
    avgConversionRate: number;
    totalUsers: number;
  };
}

export interface PredictiveVisualizationData {
  churnRisk: {
    riskLevel: 'low' | 'medium' | 'high';
    customerCount: number;
    percentage: number;
    avgChurnProbability: number;
  }[];
  revenueForecasting: {
    date: string;
    actualRevenue?: number;
    predictedRevenue: number;
    confidenceLower: number;
    confidenceUpper: number;
  }[];
  anomalies: {
    timestamp: string;
    metric: string;
    value: number;
    expectedValue: number;
    anomalyScore: number;
    severity: 'low' | 'medium' | 'high';
  }[];
}

export interface RealtimeMetricsData {
  timestamp: string;
  metrics: {
    totalRevenue: number;
    totalOrders: number;
    conversionRate: number;
    avgOrderValue: number;
    activeUsers: number;
  };
  trends: {
    revenueChange: number;
    ordersChange: number;
    conversionChange: number;
  };
}

// =====================================================
// ANALYTICS DATA SERVICE
// =====================================================

class AnalyticsDataService {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;

  constructor() {
    // Use globalThis to access Deno in Fresh environment
    const env = (globalThis as { Deno?: { env: { get: (key: string) => string | undefined } } }).Deno?.env || { get: () => undefined };
    this.baseUrl = env.get("ANALYTICS_SERVICE_URL") || "http://localhost:3002";
    this.defaultHeaders = {
      "Content-Type": "application/json",
      "Accept": "application/json",
    };
  }

  /**
   * Fetch cohort visualization data
   */
  async getCohortVisualizationData(options: AnalyticsDataOptions): Promise<CohortVisualizationData> {
    try {
      const params = new URLSearchParams({
        tenant_id: options.tenantId,
        date_from: options.dateFrom || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
        date_to: options.dateTo || new Date().toISOString(),
        cohort_type: 'acquisition',
        granularity: options.granularity || 'monthly',
        include_projections: String(options.includeProjections || true),
      });

      const [analysisResponse, curvesResponse] = await Promise.all([
        fetch(`${this.baseUrl}/api/enhanced-analytics/cohorts/analysis?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/cohorts/retention-curves?${params}`, {
          headers: this.defaultHeaders,
        }),
      ]);

      if (!analysisResponse.ok || !curvesResponse.ok) {
        throw new Error("Failed to fetch cohort data from backend service");
      }

      const analysisData = await analysisResponse.json();
      const curvesData = await curvesResponse.json();

      if (!analysisData.success || !curvesData.success) {
        throw new Error(analysisData.error || curvesData.error || "Backend API returned error");
      }

      // Transform data for visualization
      const heatmapData: Array<{
        cohortId: string;
        cohortDate: string;
        period: number;
        retentionRate: number;
        customerCount: number;
      }> = [];

      if (analysisData.data.segments && Array.isArray(analysisData.data.segments)) {
        for (const segment of analysisData.data.segments) {
          if (segment.retentionData && Array.isArray(segment.retentionData)) {
            for (const retention of segment.retentionData) {
              heatmapData.push({
                cohortId: segment.cohortId,
                cohortDate: segment.cohortDate,
                period: retention.period,
                retentionRate: retention.retentionRate,
                customerCount: retention.customerCount,
              });
            }
          }
        }
      }

      return {
        heatmapData,
        retentionCurves: curvesData.data.retentionCurves || {},
        overview: analysisData.data.overview || {
          totalCohorts: 0,
          avgRetentionRate: 0,
          bestPerformingCohort: '',
          totalCustomers: 0,
        },
      };
    } catch (error) {
      logger.error("Error fetching cohort visualization data:", error);

      // Return fallback mock data instead of throwing
      return this.getMockCohortData();
    }
  }

  /**
   * Get mock cohort data for fallback
   */
  private getMockCohortData(): CohortVisualizationData {
    return {
      heatmapData: [
        { cohortId: "2024-01", cohortDate: "2024-01-01", period: 0, retentionRate: 100, customerCount: 1000 },
        { cohortId: "2024-01", cohortDate: "2024-01-01", period: 1, retentionRate: 85, customerCount: 850 },
        { cohortId: "2024-01", cohortDate: "2024-01-01", period: 2, retentionRate: 72, customerCount: 720 },
        { cohortId: "2024-02", cohortDate: "2024-02-01", period: 0, retentionRate: 100, customerCount: 1200 },
        { cohortId: "2024-02", cohortDate: "2024-02-01", period: 1, retentionRate: 88, customerCount: 1056 },
        { cohortId: "2024-03", cohortDate: "2024-03-01", period: 0, retentionRate: 100, customerCount: 1100 },
        { cohortId: "2024-03", cohortDate: "2024-03-01", period: 1, retentionRate: 90, customerCount: 990 },
      ],
      retentionCurves: {
        "2024-01": [
          { period: 0, retentionRate: 100, customerCount: 1000 },
          { period: 1, retentionRate: 85, customerCount: 850 },
          { period: 2, retentionRate: 72, customerCount: 720 },
          { period: 3, retentionRate: 65, customerCount: 650 },
        ],
        "2024-02": [
          { period: 0, retentionRate: 100, customerCount: 1200 },
          { period: 1, retentionRate: 88, customerCount: 1056 },
          { period: 2, retentionRate: 75, customerCount: 900 },
        ],
        "2024-03": [
          { period: 0, retentionRate: 100, customerCount: 1100 },
          { period: 1, retentionRate: 90, customerCount: 990 },
        ],
      },
      overview: {
        totalCohorts: 3,
        avgRetentionRate: 87.7,
        bestPerformingCohort: "2024-03",
        totalCustomers: 3300,
      },
    };
  }

  /**
   * Fetch attribution visualization data
   */
  async getAttributionVisualizationData(options: AnalyticsDataOptions): Promise<AttributionVisualizationData> {
    try {
      const params = new URLSearchParams({
        tenant_id: options.tenantId,
        date_from: options.dateFrom || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
        date_to: options.dateTo || new Date().toISOString(),
        model_types: 'first_touch,last_touch,linear,time_decay,position_based',
        include_journeys: 'true',
        include_channels: 'true',
      });

      // Fetch model comparison data
      const modelsResponse = await fetch(`/api/analytics/enhanced/attribution/models?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      // Fetch customer journey data
      const journeysResponse = await fetch(`/api/analytics/enhanced/attribution/journeys?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      // Fetch channel performance data
      const channelsResponse = await fetch(`/api/analytics/enhanced/attribution/channels?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      if (!modelsResponse.ok || !journeysResponse.ok || !channelsResponse.ok) {
        throw new Error('Failed to fetch attribution data from backend');
      }

      const modelsData = await modelsResponse.json();
      const journeysData = await journeysResponse.json();
      const channelsData = await channelsResponse.json();

      return {
        modelComparison: modelsData.data.modelComparison || [],
        customerJourneys: journeysData.data.customerJourneys || [],
        channelPerformance: channelsData.data.channelPerformance || [],
        overview: {
          totalAttributedRevenue: modelsData.data.overview?.totalAttributedRevenue || 0,
          totalConversions: modelsData.data.overview?.totalConversions || 0,
          avgPathLength: journeysData.data.overview?.avgPathLength || 0,
          topPerformingChannel: channelsData.data.overview?.topPerformingChannel || '',
          totalChannels: channelsData.data.overview?.totalChannels || 0,
          avgTimeToConversion: journeysData.data.overview?.avgTimeToConversion || 0,
        },
      };
    } catch (error) {
      logger.error("Error fetching attribution visualization data:", error);

      // Return fallback mock data instead of throwing
      return this.getMockAttributionData();
    }
  }

  /**
   * Get mock attribution data for fallback
   */
  private getMockAttributionData(): AttributionVisualizationData {
    return {
      modelComparison: [
        {
          modelName: "First Touch",
          modelType: "first_touch",
          totalAttributedRevenue: 125000,
          totalConversions: 450,
          avgOrderValue: 277.78,
          topChannels: [
            { channel: "Organic Search", attributedRevenue: 45000, attributionPercentage: 36, conversions: 162 },
            { channel: "Paid Search", attributedRevenue: 35000, attributionPercentage: 28, conversions: 126 },
            { channel: "Social Media", attributedRevenue: 25000, attributionPercentage: 20, conversions: 90 },
          ],
        },
        {
          modelName: "Last Touch",
          modelType: "last_touch",
          totalAttributedRevenue: 125000,
          totalConversions: 450,
          avgOrderValue: 277.78,
          topChannels: [
            { channel: "Email", attributedRevenue: 40000, attributionPercentage: 32, conversions: 144 },
            { channel: "Direct", attributedRevenue: 35000, attributionPercentage: 28, conversions: 126 },
            { channel: "Paid Search", attributedRevenue: 30000, attributionPercentage: 24, conversions: 108 },
          ],
        },
        {
          modelName: "Linear",
          modelType: "linear",
          totalAttributedRevenue: 125000,
          totalConversions: 450,
          avgOrderValue: 277.78,
          topChannels: [
            { channel: "Organic Search", attributedRevenue: 31250, attributionPercentage: 25, conversions: 112 },
            { channel: "Paid Search", attributedRevenue: 31250, attributionPercentage: 25, conversions: 112 },
            { channel: "Email", attributedRevenue: 25000, attributionPercentage: 20, conversions: 90 },
          ],
        },
      ],
      customerJourneys: [
        {
          journeyId: "journey_001",
          customerId: "customer_123",
          touchpoints: [
            {
              timestamp: "2024-01-01T10:00:00Z",
              channel: "Organic Search",
              source: "google",
              medium: "organic",
              attributionWeight: 0.4,
              position: 1,
            },
            {
              timestamp: "2024-01-02T14:30:00Z",
              channel: "Social Media",
              source: "facebook",
              medium: "social",
              campaign: "winter-sale",
              attributionWeight: 0.3,
              position: 2,
            },
            {
              timestamp: "2024-01-03T16:45:00Z",
              channel: "Email",
              source: "newsletter",
              medium: "email",
              campaign: "follow-up",
              attributionWeight: 0.3,
              position: 3,
            },
          ],
          conversionValue: 299.99,
          conversionTimestamp: "2024-01-03T17:00:00Z",
          pathLength: 3,
          timeToConversion: 55, // hours
        },
      ],
      channelPerformance: [
        {
          channel: "Organic Search",
          source: "google",
          medium: "organic",
          totalClicks: 12500,
          totalConversions: 375,
          totalRevenue: 93750,
          conversionRate: 3.0,
          attributionWeights: {
            firstTouch: 0.36,
            lastTouch: 0.15,
            linear: 0.25,
            timeDecay: 0.28,
            positionBased: 0.31,
          },
          avgTimeToConversion: 48,
          avgOrderValue: 250,
        },
        {
          channel: "Paid Search",
          source: "google",
          medium: "cpc",
          totalClicks: 8500,
          totalConversions: 255,
          totalRevenue: 63750,
          conversionRate: 3.0,
          attributionWeights: {
            firstTouch: 0.28,
            lastTouch: 0.24,
            linear: 0.25,
            timeDecay: 0.26,
            positionBased: 0.26,
          },
          avgTimeToConversion: 24,
          avgOrderValue: 250,
        },
      ],
      overview: {
        totalAttributedRevenue: 125000,
        totalConversions: 450,
        avgPathLength: 2.8,
        topPerformingChannel: "Organic Search",
        totalChannels: 6,
        avgTimeToConversion: 36,
      },
    };
  }

  /**
   * Fetch real-time visualization data
   */
  async getRealtimeVisualizationData(options: AnalyticsDataOptions): Promise<RealtimeVisualizationData> {
    try {
      const params = new URLSearchParams({
        tenant_id: options.tenantId,
        time_window: '24h', // Default to 24 hours for real-time data
        include_events: 'true',
        include_funnel: 'true',
        include_geography: 'true',
      });

      // Fetch real-time metrics
      const metricsResponse = await fetch(`/api/analytics/enhanced/realtime/metrics?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      // Fetch event stream
      const eventsResponse = await fetch(`/api/analytics/enhanced/realtime/events?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      // Fetch funnel data
      const funnelResponse = await fetch(`/api/analytics/enhanced/realtime/funnel?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      // Fetch geography data
      const geographyResponse = await fetch(`/api/analytics/enhanced/realtime/geography?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      if (!metricsResponse.ok || !eventsResponse.ok || !funnelResponse.ok || !geographyResponse.ok) {
        throw new Error('Failed to fetch real-time data from backend');
      }

      const [metricsData, eventsData, funnelData, geographyData] = await Promise.all([
        metricsResponse.json(),
        eventsResponse.json(),
        funnelResponse.json(),
        geographyResponse.json(),
      ]);

      return {
        metrics: metricsData.data.metrics || {},
        eventStream: eventsData.data.eventStream || [],
        funnelData: funnelData.data.funnelData || [],
        geographyData: geographyData.data.geographyData || [],
        topPages: metricsData.data.topPages || [],
        topProducts: metricsData.data.topProducts || [],
        overview: {
          totalActiveUsers: metricsData.data.overview?.totalActiveUsers || 0,
          totalPageViews: metricsData.data.overview?.totalPageViews || 0,
          totalConversions: metricsData.data.overview?.totalConversions || 0,
          totalRevenue: metricsData.data.overview?.totalRevenue || 0,
          peakActiveUsers: metricsData.data.overview?.peakActiveUsers || 0,
          avgSessionDuration: metricsData.data.overview?.avgSessionDuration || 0,
        },
      };
    } catch (error) {
      logger.error("Error fetching real-time visualization data:", error);

      // Return fallback mock data instead of throwing
      return this.getMockRealtimeData();
    }
  }

  /**
   * Get mock real-time data for fallback
   */
  private getMockRealtimeData(): RealtimeVisualizationData {
    const now = new Date();
    const mockEvents = [];

    // Generate mock events for the last hour
    for (let i = 0; i < 20; i++) {
      const eventTime = new Date(now.getTime() - (i * 3 * 60 * 1000)); // Every 3 minutes
      const eventTypes = ['page_view', 'purchase', 'add_to_cart', 'signup', 'click'] as const;
      const countries = ['United States', 'United Kingdom', 'Canada', 'Germany', 'France'];
      const cities = ['New York', 'London', 'Toronto', 'Berlin', 'Paris'];
      const devices = ['Desktop', 'Mobile', 'Tablet'];
      const sources = ['organic', 'direct', 'social', 'email', 'paid'];

      mockEvents.push({
        eventId: `event_${i + 1}`,
        timestamp: eventTime.toISOString(),
        eventType: eventTypes[Math.floor(Math.random() * eventTypes.length)],
        userId: Math.random() > 0.3 ? `user_${Math.floor(Math.random() * 1000)}` : undefined,
        sessionId: `session_${Math.floor(Math.random() * 100)}`,
        page: Math.random() > 0.5 ? `/product/${Math.floor(Math.random() * 50)}` : '/',
        product: Math.random() > 0.7 ? `Product ${Math.floor(Math.random() * 20)}` : undefined,
        value: Math.random() > 0.8 ? Math.floor(Math.random() * 500) + 50 : undefined,
        country: countries[Math.floor(Math.random() * countries.length)],
        city: cities[Math.floor(Math.random() * cities.length)],
        device: devices[Math.floor(Math.random() * devices.length)],
        source: sources[Math.floor(Math.random() * sources.length)],
      });
    }

    return {
      metrics: {
        activeUsers: 1247,
        pageViews: 8934,
        conversions: 156,
        revenue: 23450,
        bounceRate: 34.2,
        avgSessionDuration: 245,
        trends: {
          activeUsers: 'up',
          pageViews: 'up',
          conversions: 'stable',
          revenue: 'up',
        },
      },
      eventStream: mockEvents,
      funnelData: [
        { stage: 'Landing Page', stageOrder: 1, visitors: 1247, conversions: 1247, conversionRate: 100, dropOffRate: 0, avgTimeInStage: 45 },
        { stage: 'Product View', stageOrder: 2, visitors: 1247, conversions: 892, conversionRate: 71.5, dropOffRate: 28.5, avgTimeInStage: 120 },
        { stage: 'Add to Cart', stageOrder: 3, visitors: 892, conversions: 445, conversionRate: 49.9, dropOffRate: 50.1, avgTimeInStage: 90 },
        { stage: 'Checkout', stageOrder: 4, visitors: 445, conversions: 267, conversionRate: 60.0, dropOffRate: 40.0, avgTimeInStage: 180 },
        { stage: 'Purchase', stageOrder: 5, visitors: 267, conversions: 156, conversionRate: 58.4, dropOffRate: 41.6, avgTimeInStage: 60 },
      ],
      geographyData: [
        { country: 'United States', countryCode: 'US', visitors: 456, pageViews: 2134, conversions: 67, revenue: 8900, latitude: 39.8283, longitude: -98.5795 },
        { country: 'United Kingdom', countryCode: 'GB', visitors: 234, pageViews: 1456, conversions: 34, revenue: 4500, latitude: 55.3781, longitude: -3.4360 },
        { country: 'Canada', countryCode: 'CA', visitors: 189, pageViews: 987, conversions: 23, revenue: 3200, latitude: 56.1304, longitude: -106.3468 },
        { country: 'Germany', countryCode: 'DE', visitors: 167, pageViews: 834, conversions: 19, revenue: 2800, latitude: 51.1657, longitude: 10.4515 },
        { country: 'France', countryCode: 'FR', visitors: 134, pageViews: 678, conversions: 13, revenue: 2100, latitude: 46.2276, longitude: 2.2137 },
      ],
      topPages: [
        { page: '/', pageViews: 2345, uniqueVisitors: 1876, avgTimeOnPage: 145, bounceRate: 32.1 },
        { page: '/products', pageViews: 1567, uniqueVisitors: 1234, avgTimeOnPage: 234, bounceRate: 28.5 },
        { page: '/product/123', pageViews: 987, uniqueVisitors: 789, avgTimeOnPage: 312, bounceRate: 25.3 },
        { page: '/checkout', pageViews: 456, uniqueVisitors: 398, avgTimeOnPage: 456, bounceRate: 15.2 },
        { page: '/about', pageViews: 234, uniqueVisitors: 198, avgTimeOnPage: 123, bounceRate: 45.6 },
      ],
      topProducts: [
        { productId: 'prod_123', productName: 'Wireless Headphones', views: 567, purchases: 45, revenue: 6750, conversionRate: 7.9 },
        { productId: 'prod_456', productName: 'Smart Watch', views: 432, purchases: 34, revenue: 8500, conversionRate: 7.8 },
        { productId: 'prod_789', productName: 'Laptop Stand', views: 345, purchases: 28, revenue: 2800, conversionRate: 8.1 },
        { productId: 'prod_012', productName: 'USB-C Cable', views: 298, purchases: 23, revenue: 690, conversionRate: 7.7 },
        { productId: 'prod_345', productName: 'Bluetooth Speaker', views: 267, purchases: 19, revenue: 2850, conversionRate: 7.1 },
      ],
      overview: {
        totalActiveUsers: 1247,
        totalPageViews: 8934,
        totalConversions: 156,
        totalRevenue: 23450,
        peakActiveUsers: 1456,
        avgSessionDuration: 245,
      },
    };
  }

  /**
   * Fetch CLV visualization data
   */
  async getCLVVisualizationData(options: AnalyticsDataOptions): Promise<CLVVisualizationData> {
    try {
      const params = new URLSearchParams({
        tenant_id: options.tenantId,
        date_from: options.dateFrom || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
        date_to: options.dateTo || new Date().toISOString(),
        model_type: 'auto',
        include_segmentation: 'true',
        include_predictions: String(options.includeProjections || true),
      });

      const [distributionResponse, segmentsResponse, trendsResponse] = await Promise.all([
        fetch(`${this.baseUrl}/api/enhanced-analytics/clv/distribution?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/clv/segments?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/clv/trends?${params}`, {
          headers: this.defaultHeaders,
        }),
      ]);

      if (!distributionResponse.ok || !segmentsResponse.ok || !trendsResponse.ok) {
        throw new Error("Failed to fetch CLV data");
      }

      const distributionData = await distributionResponse.json();
      const segmentsData = await segmentsResponse.json();
      const trendsData = await trendsResponse.json();

      return {
        distribution: distributionData.data.distribution,
        segments: segmentsData.data.segments,
        trends: trendsData.data.trends,
      };
    } catch (error) {
      logger.error("Error fetching CLV visualization data:", error);
      throw error;
    }
  }

  /**
   * Fetch funnel visualization data
   */
  async getFunnelVisualizationData(options: AnalyticsDataOptions): Promise<FunnelVisualizationData> {
    try {
      const params = new URLSearchParams({
        tenant_id: options.tenantId,
        date_from: options.dateFrom || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        date_to: options.dateTo || new Date().toISOString(),
        funnel_type: 'conversion',
        include_flows: 'true',
      });

      const [stepsResponse, flowsResponse] = await Promise.all([
        fetch(`${this.baseUrl}/api/enhanced-analytics/funnels/conversion-steps?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/funnels/customer-flows?${params}`, {
          headers: this.defaultHeaders,
        }),
      ]);

      if (!stepsResponse.ok || !flowsResponse.ok) {
        throw new Error("Failed to fetch funnel data");
      }

      const stepsData = await stepsResponse.json();
      const flowsData = await flowsResponse.json();

      return {
        steps: stepsData.data.steps,
        flows: flowsData.data.flows,
        overview: stepsData.data.overview,
      };
    } catch (error) {
      logger.error("Error fetching funnel visualization data:", error);
      throw error;
    }
  }

  /**
   * Fetch predictive analytics visualization data
   */
  async getPredictiveVisualizationData(options: AnalyticsDataOptions): Promise<PredictiveVisualizationData> {
    try {
      const params = new URLSearchParams({
        tenant_id: options.tenantId,
        date_from: options.dateFrom || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        date_to: options.dateTo || new Date().toISOString(),
        prediction_horizon: '12m',
      });

      const [churnResponse, revenueResponse, anomaliesResponse] = await Promise.all([
        fetch(`${this.baseUrl}/api/enhanced-analytics/predictions/churn-risk?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/predictions/revenue-forecast?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/predictions/anomaly-detection?${params}`, {
          headers: this.defaultHeaders,
        }),
      ]);

      if (!churnResponse.ok || !revenueResponse.ok || !anomaliesResponse.ok) {
        throw new Error("Failed to fetch predictive analytics data");
      }

      const churnData = await churnResponse.json();
      const revenueData = await revenueResponse.json();
      const anomaliesData = await anomaliesResponse.json();

      return {
        churnRisk: churnData.data.riskDistribution,
        revenueForecasting: revenueData.data.forecasts,
        anomalies: anomaliesData.data.anomalies,
      };
    } catch (error) {
      logger.error("Error fetching predictive visualization data:", error);
      throw error;
    }
  }

  /**
   * Fetch real-time metrics data
   */
  async getRealtimeMetricsData(tenantId: string): Promise<RealtimeMetricsData> {
    try {
      const params = new URLSearchParams({
        tenant_id: tenantId,
      });

      const response = await fetch(`${this.baseUrl}/api/realtime/metrics?${params}`, {
        headers: this.defaultHeaders,
      });

      if (!response.ok) {
        throw new Error("Failed to fetch real-time metrics");
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      logger.error("Error fetching real-time metrics data:", error);
      throw error;
    }
  }

  /**
   * Get all dashboard data in a single request for server-side rendering
   */
  async getDashboardData(options: AnalyticsDataOptions) {
    try {
      const [cohortData, clvData, funnelData, predictiveData, realtimeData] = await Promise.all([
        this.getCohortVisualizationData(options),
        this.getCLVVisualizationData(options),
        this.getFunnelVisualizationData(options),
        this.getPredictiveVisualizationData(options),
        this.getRealtimeMetricsData(options.tenantId),
      ]);

      return {
        cohorts: cohortData,
        clv: clvData,
        funnels: funnelData,
        predictive: predictiveData,
        realtime: realtimeData,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      logger.error("Error fetching dashboard data:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const analyticsDataService = new AnalyticsDataService();
