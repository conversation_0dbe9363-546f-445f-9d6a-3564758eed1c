// Analytics Data Service for D3.js Visualizations
// Week 17-18 Implementation: Server-side data fetching for enhanced analytics

// Simple logger fallback
const logger = {
  error: (message: string, error?: unknown) => {
    console.error(message, error);
  },
  warn: (message: string, data?: unknown) => {
    console.warn(message, data);
  },
  info: (message: string, data?: unknown) => {
    console.info(message, data);
  },
};

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface AnalyticsDataOptions {
  tenantId: string;
  dateFrom?: string;
  dateTo?: string;
  granularity?: 'daily' | 'weekly' | 'monthly';
  includeProjections?: boolean;
}

export interface CohortVisualizationData {
  heatmapData: {
    cohortId: string;
    cohortDate: string;
    period: number;
    retentionRate: number;
    customerCount: number;
  }[];
  retentionCurves: {
    [cohortId: string]: {
      period: number;
      retentionRate: number;
      customerCount: number;
    }[];
  };
  overview: {
    totalCohorts: number;
    avgRetentionRate: number;
    bestPerformingCohort: string;
    totalCustomers: number;
  };
}

export interface CLVVisualizationData {
  distribution: {
    clvRange: string;
    customerCount: number;
    totalClv: number;
    percentage: number;
  }[];
  segments: {
    segmentName: string;
    customerCount: number;
    avgClv: number;
    totalClv: number;
    color: string;
  }[];
  trends: {
    date: string;
    avgClv: number;
    medianClv: number;
    totalClv: number;
  }[];
}

export interface FunnelVisualizationData {
  steps: {
    stepId: string;
    stepName: string;
    stepOrder: number;
    totalUsers: number;
    convertedUsers: number;
    conversionRate: number;
    dropoffRate: number;
  }[];
  flows: {
    source: string;
    target: string;
    value: number;
  }[];
  overview: {
    totalFunnels: number;
    avgConversionRate: number;
    totalUsers: number;
  };
}

export interface PredictiveVisualizationData {
  churnRisk: {
    riskLevel: 'low' | 'medium' | 'high';
    customerCount: number;
    percentage: number;
    avgChurnProbability: number;
  }[];
  revenueForecasting: {
    date: string;
    actualRevenue?: number;
    predictedRevenue: number;
    confidenceLower: number;
    confidenceUpper: number;
  }[];
  anomalies: {
    timestamp: string;
    metric: string;
    value: number;
    expectedValue: number;
    anomalyScore: number;
    severity: 'low' | 'medium' | 'high';
  }[];
}

export interface RealtimeMetricsData {
  timestamp: string;
  metrics: {
    totalRevenue: number;
    totalOrders: number;
    conversionRate: number;
    avgOrderValue: number;
    activeUsers: number;
  };
  trends: {
    revenueChange: number;
    ordersChange: number;
    conversionChange: number;
  };
}

// =====================================================
// ANALYTICS DATA SERVICE
// =====================================================

class AnalyticsDataService {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;

  constructor() {
    // Use globalThis to access Deno in Fresh environment
    const env = (globalThis as { Deno?: { env: { get: (key: string) => string | undefined } } }).Deno?.env || { get: () => undefined };
    this.baseUrl = env.get("ANALYTICS_SERVICE_URL") || "http://localhost:3002";
    this.defaultHeaders = {
      "Content-Type": "application/json",
      "Accept": "application/json",
    };
  }

  /**
   * Fetch cohort visualization data
   */
  async getCohortVisualizationData(options: AnalyticsDataOptions): Promise<CohortVisualizationData> {
    try {
      const params = new URLSearchParams({
        tenant_id: options.tenantId,
        date_from: options.dateFrom || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
        date_to: options.dateTo || new Date().toISOString(),
        cohort_type: 'acquisition',
        granularity: options.granularity || 'monthly',
        include_projections: String(options.includeProjections || true),
      });

      const [analysisResponse, curvesResponse] = await Promise.all([
        fetch(`${this.baseUrl}/api/enhanced-analytics/cohorts/analysis?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/cohorts/retention-curves?${params}`, {
          headers: this.defaultHeaders,
        }),
      ]);

      if (!analysisResponse.ok || !curvesResponse.ok) {
        throw new Error("Failed to fetch cohort data from backend service");
      }

      const analysisData = await analysisResponse.json();
      const curvesData = await curvesResponse.json();

      if (!analysisData.success || !curvesData.success) {
        throw new Error(analysisData.error || curvesData.error || "Backend API returned error");
      }

      // Transform data for visualization
      const heatmapData: Array<{
        cohortId: string;
        cohortDate: string;
        period: number;
        retentionRate: number;
        customerCount: number;
      }> = [];

      if (analysisData.data.segments && Array.isArray(analysisData.data.segments)) {
        for (const segment of analysisData.data.segments) {
          if (segment.retentionData && Array.isArray(segment.retentionData)) {
            for (const retention of segment.retentionData) {
              heatmapData.push({
                cohortId: segment.cohortId,
                cohortDate: segment.cohortDate,
                period: retention.period,
                retentionRate: retention.retentionRate,
                customerCount: retention.customerCount,
              });
            }
          }
        }
      }

      return {
        heatmapData,
        retentionCurves: curvesData.data.retentionCurves || {},
        overview: analysisData.data.overview || {
          totalCohorts: 0,
          avgRetentionRate: 0,
          bestPerformingCohort: '',
          totalCustomers: 0,
        },
      };
    } catch (error) {
      logger.error("Error fetching cohort visualization data:", error);

      // Return fallback mock data instead of throwing
      return this.getMockCohortData();
    }
  }

  /**
   * Get mock cohort data for fallback
   */
  private getMockCohortData(): CohortVisualizationData {
    return {
      heatmapData: [
        { cohortId: "2024-01", cohortDate: "2024-01-01", period: 0, retentionRate: 100, customerCount: 1000 },
        { cohortId: "2024-01", cohortDate: "2024-01-01", period: 1, retentionRate: 85, customerCount: 850 },
        { cohortId: "2024-01", cohortDate: "2024-01-01", period: 2, retentionRate: 72, customerCount: 720 },
        { cohortId: "2024-02", cohortDate: "2024-02-01", period: 0, retentionRate: 100, customerCount: 1200 },
        { cohortId: "2024-02", cohortDate: "2024-02-01", period: 1, retentionRate: 88, customerCount: 1056 },
        { cohortId: "2024-03", cohortDate: "2024-03-01", period: 0, retentionRate: 100, customerCount: 1100 },
        { cohortId: "2024-03", cohortDate: "2024-03-01", period: 1, retentionRate: 90, customerCount: 990 },
      ],
      retentionCurves: {
        "2024-01": [
          { period: 0, retentionRate: 100, customerCount: 1000 },
          { period: 1, retentionRate: 85, customerCount: 850 },
          { period: 2, retentionRate: 72, customerCount: 720 },
          { period: 3, retentionRate: 65, customerCount: 650 },
        ],
        "2024-02": [
          { period: 0, retentionRate: 100, customerCount: 1200 },
          { period: 1, retentionRate: 88, customerCount: 1056 },
          { period: 2, retentionRate: 75, customerCount: 900 },
        ],
        "2024-03": [
          { period: 0, retentionRate: 100, customerCount: 1100 },
          { period: 1, retentionRate: 90, customerCount: 990 },
        ],
      },
      overview: {
        totalCohorts: 3,
        avgRetentionRate: 87.7,
        bestPerformingCohort: "2024-03",
        totalCustomers: 3300,
      },
    };
  }

  /**
   * Fetch CLV visualization data
   */
  async getCLVVisualizationData(options: AnalyticsDataOptions): Promise<CLVVisualizationData> {
    try {
      const params = new URLSearchParams({
        tenant_id: options.tenantId,
        date_from: options.dateFrom || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
        date_to: options.dateTo || new Date().toISOString(),
        model_type: 'auto',
        include_segmentation: 'true',
        include_predictions: String(options.includeProjections || true),
      });

      const [distributionResponse, segmentsResponse, trendsResponse] = await Promise.all([
        fetch(`${this.baseUrl}/api/enhanced-analytics/clv/distribution?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/clv/segments?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/clv/trends?${params}`, {
          headers: this.defaultHeaders,
        }),
      ]);

      if (!distributionResponse.ok || !segmentsResponse.ok || !trendsResponse.ok) {
        throw new Error("Failed to fetch CLV data");
      }

      const distributionData = await distributionResponse.json();
      const segmentsData = await segmentsResponse.json();
      const trendsData = await trendsResponse.json();

      return {
        distribution: distributionData.data.distribution,
        segments: segmentsData.data.segments,
        trends: trendsData.data.trends,
      };
    } catch (error) {
      logger.error("Error fetching CLV visualization data:", error);
      throw error;
    }
  }

  /**
   * Fetch funnel visualization data
   */
  async getFunnelVisualizationData(options: AnalyticsDataOptions): Promise<FunnelVisualizationData> {
    try {
      const params = new URLSearchParams({
        tenant_id: options.tenantId,
        date_from: options.dateFrom || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        date_to: options.dateTo || new Date().toISOString(),
        funnel_type: 'conversion',
        include_flows: 'true',
      });

      const [stepsResponse, flowsResponse] = await Promise.all([
        fetch(`${this.baseUrl}/api/enhanced-analytics/funnels/conversion-steps?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/funnels/customer-flows?${params}`, {
          headers: this.defaultHeaders,
        }),
      ]);

      if (!stepsResponse.ok || !flowsResponse.ok) {
        throw new Error("Failed to fetch funnel data");
      }

      const stepsData = await stepsResponse.json();
      const flowsData = await flowsResponse.json();

      return {
        steps: stepsData.data.steps,
        flows: flowsData.data.flows,
        overview: stepsData.data.overview,
      };
    } catch (error) {
      logger.error("Error fetching funnel visualization data:", error);
      throw error;
    }
  }

  /**
   * Fetch predictive analytics visualization data
   */
  async getPredictiveVisualizationData(options: AnalyticsDataOptions): Promise<PredictiveVisualizationData> {
    try {
      const params = new URLSearchParams({
        tenant_id: options.tenantId,
        date_from: options.dateFrom || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        date_to: options.dateTo || new Date().toISOString(),
        prediction_horizon: '12m',
      });

      const [churnResponse, revenueResponse, anomaliesResponse] = await Promise.all([
        fetch(`${this.baseUrl}/api/enhanced-analytics/predictions/churn-risk?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/predictions/revenue-forecast?${params}`, {
          headers: this.defaultHeaders,
        }),
        fetch(`${this.baseUrl}/api/enhanced-analytics/predictions/anomaly-detection?${params}`, {
          headers: this.defaultHeaders,
        }),
      ]);

      if (!churnResponse.ok || !revenueResponse.ok || !anomaliesResponse.ok) {
        throw new Error("Failed to fetch predictive analytics data");
      }

      const churnData = await churnResponse.json();
      const revenueData = await revenueResponse.json();
      const anomaliesData = await anomaliesResponse.json();

      return {
        churnRisk: churnData.data.riskDistribution,
        revenueForecasting: revenueData.data.forecasts,
        anomalies: anomaliesData.data.anomalies,
      };
    } catch (error) {
      logger.error("Error fetching predictive visualization data:", error);
      throw error;
    }
  }

  /**
   * Fetch real-time metrics data
   */
  async getRealtimeMetricsData(tenantId: string): Promise<RealtimeMetricsData> {
    try {
      const params = new URLSearchParams({
        tenant_id: tenantId,
      });

      const response = await fetch(`${this.baseUrl}/api/realtime/metrics?${params}`, {
        headers: this.defaultHeaders,
      });

      if (!response.ok) {
        throw new Error("Failed to fetch real-time metrics");
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      logger.error("Error fetching real-time metrics data:", error);
      throw error;
    }
  }

  /**
   * Get all dashboard data in a single request for server-side rendering
   */
  async getDashboardData(options: AnalyticsDataOptions) {
    try {
      const [cohortData, clvData, funnelData, predictiveData, realtimeData] = await Promise.all([
        this.getCohortVisualizationData(options),
        this.getCLVVisualizationData(options),
        this.getFunnelVisualizationData(options),
        this.getPredictiveVisualizationData(options),
        this.getRealtimeMetricsData(options.tenantId),
      ]);

      return {
        cohorts: cohortData,
        clv: clvData,
        funnels: funnelData,
        predictive: predictiveData,
        realtime: realtimeData,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      logger.error("Error fetching dashboard data:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const analyticsDataService = new AnalyticsDataService();
