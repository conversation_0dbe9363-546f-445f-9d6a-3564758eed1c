import { type PageProps } from "$fresh/server.ts";

export default function App({ Component }: PageProps) {
  return (
    <html lang="en">
      <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>E-commerce Analytics Dashboard</title>
        <meta name="description" content="Comprehensive e-commerce analytics and branded link tracking platform" />

        {/* Critical CSS inline to prevent FOUC */}
        <style>{`
          /* Critical styles to prevent FOUC */
          html { font-family: system-ui, -apple-system, sans-serif; }
          body {
            margin: 0;
            background-color: #f9fafb;
            color: #111827;
            font-family: 'Inter', system-ui, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }
          @media (prefers-color-scheme: dark) {
            body { background-color: #111827; color: #f9fafb; }
          }
          .dark body { background-color: #111827; color: #f9fafb; }
          /* Loading state */
          .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
          }
          @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
          }
        `}</style>

        {/* Preload critical resources */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />

        {/* Load main stylesheet with high priority */}
        <link rel="stylesheet" href="/styles.css" />
      </head>
      <body class="bg-gray-50 dark:bg-gray-900 font-sans antialiased transition-colors duration-200">
        <Component />
      </body>
    </html>
  );
}
