import { defineRoute } from "$fresh/server.ts";

export default defineRoute(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  const integrations = [
    {
      name: "Shopify",
      description: "Connect your Shopify store to track e-commerce conversions",
      icon: "🛍️",
      status: "available",
      connected: false
    },
    {
      name: "WooCommerce",
      description: "Integrate with WooCommerce for WordPress e-commerce tracking",
      icon: "🛒",
      status: "available",
      connected: false
    },
    {
      name: "eBay",
      description: "Track eBay marketplace sales and performance",
      icon: "🏪",
      status: "available",
      connected: false
    },
    {
      name: "Google Analytics",
      description: "Enhanced analytics with Google Analytics integration",
      icon: "📊",
      status: "available",
      connected: false
    },
    {
      name: "Facebook Pixel",
      description: "Track Facebook ad conversions and optimize campaigns",
      icon: "📘",
      status: "available",
      connected: false
    },
    {
      name: "Stripe",
      description: "Payment processing and revenue tracking",
      icon: "💳",
      status: "available",
      connected: false
    }
  ];

  return (
    <div class="integrations-page">
      {/* Page Header */}
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Integrations
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          Connect your favorite tools and platforms to enhance your analytics.
        </p>
      </div>

      {/* Integration Stats */}
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Connected</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">0</p>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Available</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{integrations.length}</p>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Data Synced</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">0</p>
            </div>
          </div>
        </div>
      </div>

      {/* Integrations Grid */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {integrations.map((integration, index) => (
          <div key={index} class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-medium transition-shadow">
            <div class="flex items-start justify-between">
              <div class="flex items-center">
                <div class="text-2xl mr-3">{integration.icon}</div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {integration.name}
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {integration.description}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                {integration.connected ? (
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                    Connected
                  </span>
                ) : (
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                    Available
                  </span>
                )}
              </div>
            </div>
            
            <div class="mt-4">
              {integration.connected ? (
                <div class="flex space-x-2">
                  <button
                    type="button"
                    class="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-medium text-sm"
                  >
                    Configure
                  </button>
                  <button
                    type="button"
                    class="flex-1 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 px-4 py-2 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/30 transition-colors font-medium text-sm"
                  >
                    Disconnect
                  </button>
                </div>
              ) : (
                <button
                  type="button"
                  class="w-full bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors font-medium text-sm"
                >
                  Connect
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Help Section */}
      <div class="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">
              Need Help with Integrations?
            </h3>
            <p class="text-blue-700 dark:text-blue-300 mt-1">
              Check out our integration guides and documentation to get started quickly.
            </p>
            <div class="mt-4">
              <a
                href="#"
                class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm"
              >
                View Documentation →
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});
