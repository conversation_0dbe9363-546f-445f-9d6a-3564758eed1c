// Test page for real-time streaming infrastructure
// Week 17-18 Implementation: Testing SSE integration with D3.js visualizations

import { PageProps } from "$fresh/server.ts";
import RealtimeMetricsStream from "../islands/RealtimeMetricsStream.tsx";
import D3ChurnGaugeRealtime from "../islands/charts/D3ChurnGaugeRealtime.tsx";
import type { ChurnGaugeData } from "../islands/charts/D3ChurnGauge.tsx";

// Mock initial data for testing
const mockInitialChurnData: ChurnGaugeData = {
  totalCustomers: 1000,
  avgChurnProbability: 0.28,
  highRiskThreshold: 0.7,
  criticalRiskThreshold: 0.85,
  riskDistribution: [
    {
      riskLevel: 'low',
      customerCount: 600,
      percentage: 0.6,
      avgChurnProbability: 0.12,
      avgTimeToChurn: 180,
      interventionRequired: false
    },
    {
      riskLevel: 'medium',
      customerCount: 250,
      percentage: 0.25,
      avgChurnProbability: 0.42,
      avgTimeToChurn: 90,
      interventionRequired: false
    },
    {
      riskLevel: 'high',
      customerCount: 120,
      percentage: 0.12,
      avgChurnProbability: 0.72,
      avgTimeToChurn: 45,
      interventionRequired: true
    },
    {
      riskLevel: 'critical',
      customerCount: 30,
      percentage: 0.03,
      avgChurnProbability: 0.89,
      avgTimeToChurn: 15,
      interventionRequired: true
    }
  ]
};

export default function TestRealtimeStreaming(props: PageProps) {
  // Mock tenant ID for testing
  const testTenantId = "test-tenant-123";

  return (
    <div class="min-h-screen bg-gray-50 py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-2">
            Real-time Streaming Infrastructure Test
          </h1>
          <p class="text-gray-600">
            Testing Server-Sent Events integration with D3.js visualizations for live data updates
          </p>
        </div>

        {/* Connection Status */}
        <div class="mb-8 bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Real-time Connection Status</h2>
          <p class="text-gray-600 mb-4">
            Monitor the real-time streaming connection status and performance metrics
          </p>
          <RealtimeMetricsStream
            tenantId={testTenantId}
            options={{
              dataTypes: ['metrics', 'predictions'],
              updateInterval: 3000,
              includeHistorical: false,
              autoReconnect: true,
              maxReconnectAttempts: 5,
            }}
            showConnectionStatus={true}
            onDataUpdate={(data) => {
              console.log('Real-time data update:', data);
            }}
            onConnectionChange={(connection) => {
              console.log('Connection status changed:', connection);
            }}
            onError={(error) => {
              console.error('Real-time streaming error:', error);
            }}
          />
        </div>

        {/* Real-time Churn Gauge */}
        <div class="mb-8 bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Real-time Customer Churn Risk Gauge</h2>
          <p class="text-gray-600 mb-6">
            Interactive gauge with live updates showing customer churn risk with smooth animations
          </p>
          <D3ChurnGaugeRealtime
            tenantId={testTenantId}
            initialData={mockInitialChurnData}
            width={600}
            height={400}
            title="Live Customer Churn Risk Analysis"
            showRiskBreakdown={true}
            enableRealtimeUpdates={true}
            animationDuration={750}
            onRiskLevelClick={(riskLevel, event) => {
              console.log('Risk level clicked:', riskLevel);
            }}
            onRealtimeUpdate={(data) => {
              console.log('Churn gauge real-time update:', data);
            }}
          />
        </div>

        {/* Performance Metrics */}
        <div class="mb-8 bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Performance Metrics</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-blue-50 rounded-lg p-4">
              <h3 class="text-lg font-semibold text-blue-900 mb-2">Connection Latency</h3>
              <div class="text-2xl font-bold text-blue-700" id="latency-display">-- ms</div>
              <p class="text-sm text-blue-600 mt-1">Target: &lt;100ms</p>
            </div>
            
            <div class="bg-green-50 rounded-lg p-4">
              <h3 class="text-lg font-semibold text-green-900 mb-2">Update Frequency</h3>
              <div class="text-2xl font-bold text-green-700" id="frequency-display">-- /min</div>
              <p class="text-sm text-green-600 mt-1">Real-time updates</p>
            </div>
            
            <div class="bg-purple-50 rounded-lg p-4">
              <h3 class="text-lg font-semibold text-purple-900 mb-2">Data Points</h3>
              <div class="text-2xl font-bold text-purple-700" id="datapoints-display">--</div>
              <p class="text-sm text-purple-600 mt-1">Total received</p>
            </div>
          </div>
        </div>

        {/* Real-time Data Log */}
        <div class="mb-8 bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Real-time Data Log</h2>
          <p class="text-gray-600 mb-4">
            Live stream of incoming data updates (last 10 entries)
          </p>
          <div class="bg-gray-900 rounded-lg p-4 h-64 overflow-y-auto">
            <div id="data-log" class="text-green-400 font-mono text-sm space-y-1">
              <div class="text-gray-500">Waiting for real-time data...</div>
            </div>
          </div>
        </div>

        {/* Testing Controls */}
        <div class="mb-8 bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Testing Controls</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-3">Connection Management</h3>
              <div class="space-y-2">
                <button 
                  class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                  onclick="testReconnect()"
                >
                  Test Reconnection
                </button>
                <button 
                  class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                  onclick="testDisconnect()"
                >
                  Test Disconnection
                </button>
                <button 
                  class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                  onclick="testConnect()"
                >
                  Test Connection
                </button>
              </div>
            </div>
            
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-3">Data Simulation</h3>
              <div class="space-y-2">
                <button 
                  class="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
                  onclick="simulateHighChurn()"
                >
                  Simulate High Churn Risk
                </button>
                <button 
                  class="w-full px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
                  onclick="simulateNormalChurn()"
                >
                  Simulate Normal Churn Risk
                </button>
                <button 
                  class="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                  onclick="clearDataLog()"
                >
                  Clear Data Log
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Technical Specifications */}
        <div class="bg-blue-50 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-blue-900 mb-3">Technical Specifications</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 class="font-medium text-blue-900 mb-2">Real-time Infrastructure</h4>
              <ul class="space-y-1 text-blue-700">
                <li>• Server-Sent Events (SSE) protocol</li>
                <li>• Multi-tenant data isolation</li>
                <li>• Automatic reconnection with exponential backoff</li>
                <li>• Connection pooling and management</li>
                <li>• Performance monitoring and metrics</li>
              </ul>
            </div>
            
            <div>
              <h4 class="font-medium text-blue-900 mb-2">Performance Targets</h4>
              <ul class="space-y-1 text-blue-700">
                <li>• Update latency: &lt;100ms</li>
                <li>• Chart transitions: &lt;750ms</li>
                <li>• Connection establishment: &lt;2s</li>
                <li>• Memory usage: Optimized with cleanup</li>
                <li>• Bandwidth: Differential updates only</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* JavaScript for testing controls */}
      <script dangerouslySetInnerHTML={{
        __html: `
          let updateCount = 0;
          let startTime = Date.now();
          
          function testReconnect() {
            console.log('Testing reconnection...');
            // This would trigger reconnection logic
          }
          
          function testDisconnect() {
            console.log('Testing disconnection...');
            // This would trigger disconnection
          }
          
          function testConnect() {
            console.log('Testing connection...');
            // This would trigger connection
          }
          
          function simulateHighChurn() {
            console.log('Simulating high churn risk scenario...');
            addToDataLog('SIMULATION: High churn risk scenario activated');
          }
          
          function simulateNormalChurn() {
            console.log('Simulating normal churn risk scenario...');
            addToDataLog('SIMULATION: Normal churn risk scenario activated');
          }
          
          function clearDataLog() {
            const log = document.getElementById('data-log');
            if (log) {
              log.innerHTML = '<div class="text-gray-500">Data log cleared...</div>';
            }
          }
          
          function addToDataLog(message) {
            const log = document.getElementById('data-log');
            if (log) {
              const timestamp = new Date().toLocaleTimeString();
              const entry = document.createElement('div');
              entry.innerHTML = \`[\${timestamp}] \${message}\`;
              log.insertBefore(entry, log.firstChild);
              
              // Keep only last 10 entries
              while (log.children.length > 10) {
                log.removeChild(log.lastChild);
              }
            }
          }
          
          function updateMetrics(latency, frequency, dataPoints) {
            const latencyEl = document.getElementById('latency-display');
            const frequencyEl = document.getElementById('frequency-display');
            const datapointsEl = document.getElementById('datapoints-display');
            
            if (latencyEl) latencyEl.textContent = latency + ' ms';
            if (frequencyEl) frequencyEl.textContent = frequency + ' /min';
            if (datapointsEl) datapointsEl.textContent = dataPoints;
          }
          
          // Simulate some metrics updates for demo
          setInterval(() => {
            updateCount++;
            const elapsed = (Date.now() - startTime) / 1000 / 60; // minutes
            const frequency = Math.round(updateCount / elapsed);
            const latency = Math.floor(Math.random() * 50) + 25; // 25-75ms
            
            updateMetrics(latency, frequency, updateCount);
            
            if (updateCount % 5 === 0) {
              addToDataLog(\`Metrics update #\${updateCount} - Latency: \${latency}ms\`);
            }
          }, 3000);
        `
      }} />
    </div>
  );
}
