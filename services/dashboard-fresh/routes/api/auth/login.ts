import { HandlerContext } from "$fresh/server.ts";
import { setCookie } from "$std/http/cookie.ts";
import { generateJWT, verifyPassword } from "../../../utils/auth.ts";
import { getDatabase } from "../../../utils/database.ts";

interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface UserRecord {
  id: string;
  email: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  company_name?: string;
  role: string;
  tenant_id?: string;
  is_active: boolean;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
}

export const handler = {
  async POST(req: Request, ctx: HandlerContext) {
    try {
      const body: LoginRequest = await req.json();

      if (!body.email || !body.password) {
        return Response.json(
          { success: false, error: "Email and password are required" },
          { status: 400 }
        );
      }

      // Find user by email
      const db = await getDatabase();
      const result = await db.queryObject<UserRecord>(
        "SELECT * FROM users WHERE email = $1 AND is_active = true",
        [body.email.toLowerCase()]
      );
      const user = result.rows[0] || null;

      if (!user) {
        return Response.json(
          { success: false, error: "Invalid email or password" },
          { status: 401 }
        );
      }

      // Verify password
      const isValidPassword = await verifyPassword(body.password, user.password_hash);

      if (!isValidPassword) {
        return Response.json(
          { success: false, error: "Invalid email or password" },
          { status: 401 }
        );
      }

      // Generate JWT token
      const tokenExpiry = body.rememberMe ? "30d" : "7d";
      const userForJWT = {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        companyName: user.company_name,
        role: user.role,
        tenant_id: user.tenant_id,
      };
      const token = await generateJWT(userForJWT, tokenExpiry);

      // Prepare user data (exclude sensitive information)
      const userData = {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        companyName: user.company_name,
        role: user.role,
        tenant_id: user.tenant_id,
        isActive: user.is_active,
        emailVerified: user.email_verified,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
      };

      // Create response with cookie
      const response = Response.json({
        success: true,
        data: {
          user: userData,
          token,
          expiresIn: tokenExpiry,
        },
      });

      // Set HTTP-only cookie for browser sessions
      setCookie(response.headers, {
        name: "auth_token",
        value: token,
        httpOnly: true,
        secure: Deno.env.get("DENO_ENV") === "production",
        sameSite: "Lax",
        maxAge: body.rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60, // 30 days or 7 days
        path: "/",
      });

      return response;
    } catch (error) {
      console.error("Login error:", error);
      return Response.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
};
