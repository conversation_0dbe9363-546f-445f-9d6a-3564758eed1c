import { FreshContext } from "$fresh/server.ts";
import { getUserTenantId } from "../../../utils/auth.ts";

export const handler = {
  GET(req: Request, ctx: FreshContext) {
    const user = ctx.state.user;
    
    if (!user) {
      return new Response("Unauthorized", { status: 401 });
    }

    const tenantId = getUserTenantId(user);

    // Create a readable stream for Server-Sent Events
    const stream = new ReadableStream({
      start(controller) {
        let isStreamClosed = false;

        // Helper function to safely enqueue data
        const safeEnqueue = (data: string) => {
          if (!isStreamClosed) {
            try {
              controller.enqueue(data);
            } catch (error) {
              console.error('Error sending SSE data:', error);
              isStreamClosed = true;
            }
          }
        };

        // Send initial connection message
        safeEnqueue(`data: ${JSON.stringify({
          type: 'connected',
          timestamp: new Date().toISOString(),
          message: 'Real-time metrics stream connected'
        })}\n\n`);

        // Function to generate and send mock real-time data
        const sendMetricsUpdate = () => {
          if (isStreamClosed) return;

          const data = {
            type: 'metrics_update',
            timestamp: new Date().toISOString(),
            tenantId,
            totalRevenue: Math.floor(Math.random() * 50000) + 100000,
            activeUsers: Math.floor(Math.random() * 500) + 200,
            conversionRate: parseFloat((Math.random() * 5 + 1).toFixed(2)),
            totalLinks: Math.floor(Math.random() * 100) + 50,
            recentActivity: [
              {
                id: Date.now().toString(),
                description: `New conversion: $${(Math.random() * 200 + 50).toFixed(2)}`,
                time: 'Just now',
                type: 'conversion'
              }
            ]
          };

          safeEnqueue(`data: ${JSON.stringify(data)}\n\n`);
        };

        // Send initial data
        sendMetricsUpdate();

        // Send updates every 5 seconds
        const interval = setInterval(sendMetricsUpdate, 5000);

        // Send heartbeat every 30 seconds to keep connection alive
        const heartbeat = setInterval(() => {
          if (isStreamClosed) return;

          safeEnqueue(`data: ${JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().toISOString()
          })}\n\n`);
        }, 30000);

        // Cleanup function
        const cleanup = () => {
          isStreamClosed = true;
          clearInterval(interval);
          clearInterval(heartbeat);

          try {
            if (!isStreamClosed) {
              controller.close();
            }
          } catch (_error) {
            // Stream already closed, ignore error
            console.debug('SSE stream already closed');
          }
        };

        // Handle client disconnect
        req.signal.addEventListener("abort", cleanup);
      },

      cancel() {
        // Stream was cancelled by client
        console.debug('SSE stream cancelled by client');
      }
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      },
    });
  },
};
