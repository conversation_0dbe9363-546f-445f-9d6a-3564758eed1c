import { FreshContext } from "$fresh/server.ts";
import { getUserTenantId } from "../../../../../utils/auth.ts";

export const handler = {
  async GET(req: Request, ctx: FreshContext) {
    const user = ctx.state.user;
    
    if (!user) {
      return new Response(JSON.stringify({ success: false, error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    const tenantId = getUserTenantId(user);
    const url = new URL(req.url);
    
    // Extract query parameters
    const params = new URLSearchParams(url.search);
    
    // Ensure tenant_id is set correctly
    params.set('tenant_id', tenantId);
    
    // Default parameters if not provided
    if (!params.get('time_window')) {
      params.set('time_window', '24h');
    }

    try {
      // Get backend service URL
      const analyticsServiceUrl = Deno.env.get("ANALYTICS_SERVICE_URL") || "http://localhost:3002";
      const backendUrl = `${analyticsServiceUrl}/api/enhanced-analytics/realtime/metrics?${params.toString()}`;
      
      // Forward request to backend
      const response = await fetch(backendUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Tenant-ID': tenantId,
        },
      });

      if (!response.ok) {
        throw new Error(`Backend responded with status: ${response.status}`);
      }

      const data = await response.json();
      
      return new Response(JSON.stringify(data), {
        status: 200,
        headers: { 
          "Content-Type": "application/json",
          "Cache-Control": "public, max-age=30", // 30 seconds cache for real-time data
        },
      });
    } catch (error) {
      console.error("Error fetching real-time metrics data:", error);
      
      // Return mock data as fallback
      const mockData = {
        success: true,
        data: {
          metrics: {
            activeUsers: 1247,
            pageViews: 8934,
            conversions: 156,
            revenue: 23450,
            bounceRate: 34.2,
            avgSessionDuration: 245,
            trends: {
              activeUsers: 'up',
              pageViews: 'up',
              conversions: 'stable',
              revenue: 'up',
            },
          },
          topPages: [
            { page: '/', pageViews: 2345, uniqueVisitors: 1876, avgTimeOnPage: 145, bounceRate: 32.1 },
            { page: '/products', pageViews: 1567, uniqueVisitors: 1234, avgTimeOnPage: 234, bounceRate: 28.5 },
            { page: '/product/123', pageViews: 987, uniqueVisitors: 789, avgTimeOnPage: 312, bounceRate: 25.3 },
            { page: '/checkout', pageViews: 456, uniqueVisitors: 398, avgTimeOnPage: 456, bounceRate: 15.2 },
            { page: '/about', pageViews: 234, uniqueVisitors: 198, avgTimeOnPage: 123, bounceRate: 45.6 },
          ],
          topProducts: [
            { productId: 'prod_123', productName: 'Wireless Headphones', views: 567, purchases: 45, revenue: 6750, conversionRate: 7.9 },
            { productId: 'prod_456', productName: 'Smart Watch', views: 432, purchases: 34, revenue: 8500, conversionRate: 7.8 },
            { productId: 'prod_789', productName: 'Laptop Stand', views: 345, purchases: 28, revenue: 2800, conversionRate: 8.1 },
            { productId: 'prod_012', productName: 'USB-C Cable', views: 298, purchases: 23, revenue: 690, conversionRate: 7.7 },
            { productId: 'prod_345', productName: 'Bluetooth Speaker', views: 267, purchases: 19, revenue: 2850, conversionRate: 7.1 },
          ],
          overview: {
            totalActiveUsers: 1247,
            totalPageViews: 8934,
            totalConversions: 156,
            totalRevenue: 23450,
            peakActiveUsers: 1456,
            avgSessionDuration: 245,
          },
        },
        metadata: {
          query: {
            tenantId,
            timeWindow: params.get('time_window'),
          },
          executionTime: "5ms",
          cached: false,
          fallback: true,
          realtime: true,
        },
      };

      return new Response(JSON.stringify(mockData), {
        status: 200,
        headers: { 
          "Content-Type": "application/json",
          "X-Fallback-Data": "true",
        },
      });
    }
  },
};
