import { connect, Redis } from "redis";
import { config } from "../config/config.ts";
import { logger } from "./logger.ts";

// Redis client instance
let redisClient: Redis | null = null;

/**
 * Initialize Redis connection
 */
export async function initializeRedis(): Promise<void> {
  try {
    redisClient = await connect({
      hostname: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      db: config.redis.database,
    });

    // Test connection
    await redisClient.ping();

    logger.info("Redis connection initialized successfully", {
      host: config.redis.host,
      port: config.redis.port,
      database: config.redis.database,
    });
  } catch (error) {
    logger.error("Failed to initialize Redis connection", error as Error);
    throw error;
  }
}

/**
 * Get Redis client instance
 */
export function getRedisClient(): Redis {
  if (!redisClient) {
    throw new Error("Redis client not initialized. Call initializeRedis() first.");
  }
  return redisClient;
}

/**
 * Set a key-value pair with optional expiration
 */
export async function set(
  key: string,
  value: string | number | object,
  expirationSeconds?: number,
  tenantId?: string,
): Promise<void> {
  const client = getRedisClient();
  const finalKey = tenantId ? `tenant:${tenantId}:${key}` : key;
  
  try {
    const serializedValue = typeof value === "object" ? JSON.stringify(value) : String(value);
    
    if (expirationSeconds) {
      await client.setex(finalKey, expirationSeconds, serializedValue);
    } else {
      await client.set(finalKey, serializedValue);
    }
    
    logger.debug("Redis SET operation completed", {
      key: finalKey,
      expiration: expirationSeconds,
      tenantId,
    });
  } catch (error) {
    logger.error("Redis SET operation failed", {
      key: finalKey,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Get a value by key
 */
export async function get(key: string, tenantId?: string): Promise<string | null> {
  const client = getRedisClient();
  const finalKey = tenantId ? `tenant:${tenantId}:${key}` : key;
  
  try {
    const value = await client.get(finalKey);
    
    logger.debug("Redis GET operation completed", {
      key: finalKey,
      found: value !== null,
      tenantId,
    });
    
    return value;
  } catch (error) {
    logger.error("Redis GET operation failed", {
      key: finalKey,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Get a value and parse as JSON
 */
export async function getJSON<T = unknown>(key: string, tenantId?: string): Promise<T | null> {
  const value = await get(key, tenantId);
  
  if (value === null) {
    return null;
  }
  
  try {
    return JSON.parse(value) as T;
  } catch (error) {
    logger.error("Failed to parse JSON from Redis", {
      key: tenantId ? `tenant:${tenantId}:${key}` : key,
      value: value.substring(0, 100),
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Delete a key
 */
export async function del(key: string, tenantId?: string): Promise<number> {
  const client = getRedisClient();
  const finalKey = tenantId ? `tenant:${tenantId}:${key}` : key;
  
  try {
    const result = await client.del(finalKey);
    
    logger.debug("Redis DEL operation completed", {
      key: finalKey,
      deleted: result,
      tenantId,
    });
    
    return result;
  } catch (error) {
    logger.error("Redis DEL operation failed", {
      key: finalKey,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Check if a key exists
 */
export async function exists(key: string, tenantId?: string): Promise<boolean> {
  const client = getRedisClient();
  const finalKey = tenantId ? `tenant:${tenantId}:${key}` : key;
  
  try {
    const result = await client.exists(finalKey);
    return result === 1;
  } catch (error) {
    logger.error("Redis EXISTS operation failed", {
      key: finalKey,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Set expiration for a key
 */
export async function expire(key: string, seconds: number, tenantId?: string): Promise<boolean> {
  const client = getRedisClient();
  const finalKey = tenantId ? `tenant:${tenantId}:${key}` : key;
  
  try {
    const result = await client.expire(finalKey, seconds);
    return result === 1;
  } catch (error) {
    logger.error("Redis EXPIRE operation failed", {
      key: finalKey,
      seconds,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Increment a numeric value
 */
export async function incr(key: string, tenantId?: string): Promise<number> {
  const client = getRedisClient();
  const finalKey = tenantId ? `tenant:${tenantId}:${key}` : key;

  try {
    const result = await client.incr(finalKey);

    logger.debug("Redis INCR operation completed", {
      key: finalKey,
      newValue: result,
      tenantId,
    });

    return result;
  } catch (error) {
    logger.error("Redis INCR operation failed", {
      key: finalKey,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Decrement a numeric value
 */
export async function decr(key: string, tenantId?: string): Promise<number> {
  const client = getRedisClient();
  const finalKey = tenantId ? `tenant:${tenantId}:${key}` : key;

  try {
    const result = await client.decr(finalKey);

    logger.debug("Redis DECR operation completed", {
      key: finalKey,
      newValue: result,
      tenantId,
    });

    return result;
  } catch (error) {
    logger.error("Redis DECR operation failed", {
      key: finalKey,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Get keys matching a pattern
 */
export async function keys(pattern: string, tenantId?: string): Promise<string[]> {
  const client = getRedisClient();
  const finalPattern = tenantId ? `tenant:${tenantId}:${pattern}` : pattern;
  
  try {
    const result = await client.keys(finalPattern);
    
    logger.debug("Redis KEYS operation completed", {
      pattern: finalPattern,
      count: result.length,
      tenantId,
    });
    
    return result;
  } catch (error) {
    logger.error("Redis KEYS operation failed", {
      pattern: finalPattern,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Health check for Redis connection
 */
export async function healthCheck(): Promise<boolean> {
  try {
    if (!redisClient) {
      return false;
    }
    
    const result = await redisClient.ping();
    return result === "PONG";
  } catch (error) {
    logger.error("Redis health check failed", error as Error);
    return false;
  }
}

/**
 * Close Redis connection
 */
export function closeRedis(): void {
  if (redisClient) {
    redisClient.close();
    redisClient = null;
    logger.info("Redis connection closed");
  }
}
