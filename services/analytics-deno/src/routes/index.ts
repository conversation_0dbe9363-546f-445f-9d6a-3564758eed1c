import { Application, Router } from "@oak/oak";
import { logger } from "../utils/logger.ts";
import { generateMetrics } from "../middleware/metrics.ts";
import { healthCheck as dbHealthCheck } from "../utils/database.ts";
import { healthCheck as redisHealthCheck } from "../utils/redis.ts";
import { analyticsRouter } from "./analytics.ts";
import { reportsRouter } from "./reports.ts";
import { enhancedAnalyticsRouter } from "./enhancedAnalytics.ts";
import enhancedCohortAnalysisRouter from "./enhancedCohortAnalysis.ts";
import enhancedCLVAnalysisRouter from "./enhancedCLVAnalysis.ts";
import enhancedFunnelAnalysisRouter from "./enhancedFunnelAnalysis.ts";
import enhancedPredictiveAnalyticsRouter from "./enhancedPredictiveAnalytics.ts";
import realtimeStreamRouter from "./realtimeStream.ts";

/**
 * Setup all routes for the Oak application
 */
export function setupRoutes(app: Application): void {
  const router = new Router();

  logger.info("Setting up routes");

  // Health check endpoints
  router.get("/health", (ctx) => {
    ctx.response.body = {
      success: true,
      service: "analytics-service",
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
    };
  });

  router.get("/ready", async (ctx) => {
    const dbHealth = await dbHealthCheck();
    const redisHealth = await redisHealthCheck();
    
    const isReady = dbHealth && redisHealth;
    
    ctx.response.status = isReady ? 200 : 503;
    ctx.response.body = {
      success: isReady,
      service: "analytics-service",
      status: isReady ? "ready" : "not ready",
      checks: {
        database: dbHealth ? "healthy" : "unhealthy",
        redis: redisHealth ? "healthy" : "unhealthy",
      },
      timestamp: new Date().toISOString(),
    };
  });

  router.get("/live", (ctx) => {
    ctx.response.body = {
      success: true,
      service: "analytics-service",
      status: "alive",
      timestamp: new Date().toISOString(),
    };
  });

  // Metrics endpoint
  router.get("/metrics", (ctx) => {
    ctx.response.headers.set("Content-Type", "text/plain; version=0.0.4; charset=utf-8");
    ctx.response.body = generateMetrics();
  });

  // Root endpoint
  router.get("/", (ctx) => {
    ctx.response.body = {
      success: true,
      service: "analytics-service",
      version: "1.0.0",
      environment: Deno.env.get("NODE_ENV") || "development",
      timestamp: new Date().toISOString(),
      endpoints: [
        "GET /health - Health check",
        "GET /ready - Readiness check",
        "GET /live - Liveness check",
        "GET /metrics - Prometheus metrics",
        "GET /api/analytics/* - Analytics endpoints",
        "GET /api/enhanced-analytics/* - Enhanced analytics endpoints (Phase 1)",
        "GET /api/enhanced-analytics/cohorts/* - Enhanced cohort analysis endpoints (Phase 2)",
        "GET /api/reports/* - Reports endpoints",
        "GET /api/conversions/* - Conversions endpoints",
        "GET /api/attribution/* - Attribution endpoints",
        "GET /api/forecasting/* - Forecasting endpoints",
        "GET /api/alerts/* - Alerts endpoints",
        "GET /api/exploration/* - Exploration endpoints",
        "GET /api/performance/* - Performance endpoints",
        "GET /api/quality/* - Quality endpoints",
        "GET /api/advanced/* - Advanced analytics endpoints",
        "GET /api/dashboard-enhancements/* - Dashboard enhancements endpoints",
        "GET /api/data-export/* - Data export endpoints",
      ],
    };
  });

  // API routes
  const apiRouter = new Router({ prefix: "/api" });

  // Register analytics routes
  apiRouter.use("/analytics", analyticsRouter.routes(), analyticsRouter.allowedMethods());

  // Register enhanced analytics routes (Phase 1)
  apiRouter.use("/enhanced-analytics", enhancedAnalyticsRouter.routes(), enhancedAnalyticsRouter.allowedMethods());

  // Register reports routes
  apiRouter.use("/reports", reportsRouter.routes(), reportsRouter.allowedMethods());

  // Placeholder for conversions routes
  apiRouter.get("/conversions", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Conversions endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Enhanced cohort analysis routes (Phase 2)
  apiRouter.use("/enhanced-analytics/cohorts", enhancedCohortAnalysisRouter.routes(), enhancedCohortAnalysisRouter.allowedMethods());

  // Enhanced CLV analysis routes (Phase 2 - Week 11-12)
  apiRouter.use("/enhanced-analytics/clv", enhancedCLVAnalysisRouter.routes(), enhancedCLVAnalysisRouter.allowedMethods());

  // Enhanced Funnel analysis routes (Phase 2 - Week 13-14)
  apiRouter.use("/enhanced-analytics/funnels", enhancedFunnelAnalysisRouter.routes(), enhancedFunnelAnalysisRouter.allowedMethods());

  // Enhanced Predictive Analytics routes (Phase 2 - Week 15-16)
  apiRouter.use("/enhanced-analytics", enhancedPredictiveAnalyticsRouter.routes(), enhancedPredictiveAnalyticsRouter.allowedMethods());

  // Real-time Streaming routes (Phase 2 - Week 17-18)
  apiRouter.use("/realtime", realtimeStreamRouter.routes(), realtimeStreamRouter.allowedMethods());

  // Placeholder for attribution routes
  apiRouter.get("/attribution", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Attribution endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for forecasting routes
  apiRouter.get("/forecasting", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Forecasting endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for alerts routes
  apiRouter.get("/alerts", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Alerts endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for exploration routes
  apiRouter.get("/exploration", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Exploration endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for performance routes
  apiRouter.get("/performance", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Performance endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for quality routes
  apiRouter.get("/quality", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Quality endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for advanced routes
  apiRouter.get("/advanced", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Advanced analytics endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for dashboard enhancements routes
  apiRouter.get("/dashboard-enhancements", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Dashboard enhancements endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for data export routes
  apiRouter.get("/data-export", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Data export endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // 404 handler for API routes
  apiRouter.all("/(.*)", (ctx) => {
    ctx.response.status = 404;
    ctx.response.body = {
      success: false,
      error: {
        code: "NOT_FOUND",
        message: "API endpoint not found",
      },
      timestamp: new Date().toISOString(),
    };
  });

  // Register routers
  app.use(router.routes());
  app.use(router.allowedMethods());
  app.use(apiRouter.routes());
  app.use(apiRouter.allowedMethods());

  // Global 404 handler
  app.use((ctx) => {
    ctx.response.status = 404;
    ctx.response.body = {
      success: false,
      error: {
        code: "NOT_FOUND",
        message: "Endpoint not found",
      },
      timestamp: new Date().toISOString(),
    };
  });

  logger.info("Routes setup completed");
}
