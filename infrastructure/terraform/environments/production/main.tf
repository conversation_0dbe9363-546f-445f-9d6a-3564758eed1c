# Production Environment Configuration
# Phase 3: Production Deployment - E-commerce Analytics SaaS Platform
# Main Terraform configuration for production infrastructure

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.23"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.11"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }
  }

  backend "s3" {
    bucket         = "ecommerce-analytics-terraform-state-production"
    key            = "production/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "ecommerce-analytics-terraform-locks"
  }
}

# Configure AWS Provider
provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = local.common_tags
  }
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Local values
locals {
  cluster_name = "${var.project_name}-${var.environment}"
  
  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "terraform"
    Owner       = "platform-team"
    CostCenter  = "engineering"
    CreatedAt   = timestamp()
    Region      = var.aws_region
  }
}

# Random password for database
resource "random_password" "database_password" {
  length  = 32
  special = true
}

# KMS Key for encryption
resource "aws_kms_key" "main" {
  description             = "KMS key for ${var.project_name} ${var.environment} encryption"
  deletion_window_in_days = 7
  enable_key_rotation     = true

  tags = merge(local.common_tags, {
    Name = "${var.project_name}-kms-key-${var.environment}"
  })
}

resource "aws_kms_alias" "main" {
  name          = "alias/${var.project_name}-${var.environment}"
  target_key_id = aws_kms_key.main.key_id
}

# VPC Module
module "vpc" {
  source = "../../modules/vpc"

  project_name = var.project_name
  environment  = var.environment
  common_tags  = local.common_tags

  vpc_cidr           = var.vpc_cidr
  availability_zones = var.availability_zones
  public_subnets     = var.public_subnets
  private_subnets    = var.private_subnets
  database_subnets   = var.database_subnets

  enable_nat_gateway = var.enable_nat_gateway
  enable_flow_logs   = var.enable_flow_logs

  enable_s3_endpoint       = var.enable_s3_endpoint
  enable_dynamodb_endpoint = var.enable_dynamodb_endpoint

  enable_bastion                = var.enable_bastion
  bastion_allowed_cidr_blocks   = var.bastion_allowed_cidr_blocks
  bastion_instance_type         = var.bastion_instance_type
}

# EKS Cluster Module
module "eks" {
  source = "../../modules/eks"

  cluster_name       = local.cluster_name
  kubernetes_version = var.kubernetes_version
  common_tags        = local.common_tags

  # VPC Configuration
  vpc_id             = module.vpc.vpc_id
  public_subnet_ids  = module.vpc.public_subnets
  private_subnet_ids = module.vpc.private_subnets

  # IAM Roles
  cluster_service_role_arn = module.iam.eks_cluster_role_arn
  node_group_role_arn      = module.iam.eks_node_role_arn

  # Cluster Configuration
  endpoint_private_access      = true
  endpoint_public_access       = true
  endpoint_public_access_cidrs = var.cluster_endpoint_public_access_cidrs
  additional_security_group_ids = [module.vpc.eks_cluster_security_group_id]

  # Encryption
  cluster_encryption_config_enabled    = var.enable_encryption_at_rest
  cluster_encryption_config_kms_key_id = aws_kms_key.main.arn

  # Logging
  cluster_enabled_log_types                = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
  cloudwatch_log_group_retention_in_days   = var.cloudwatch_log_retention_days
  cloudwatch_log_group_kms_key_id         = aws_kms_key.main.arn

  # Node Groups
  node_groups = var.node_groups

  # EKS Addons
  cluster_addons = {
    coredns = {
      addon_version            = "v1.10.1-eksbuild.5"
      resolve_conflicts        = "OVERWRITE"
      service_account_role_arn = null
    }
    kube-proxy = {
      addon_version            = "v1.28.2-eksbuild.2"
      resolve_conflicts        = "OVERWRITE"
      service_account_role_arn = null
    }
    vpc-cni = {
      addon_version            = "v1.15.4-eksbuild.1"
      resolve_conflicts        = "OVERWRITE"
      service_account_role_arn = null
    }
    aws-ebs-csi-driver = {
      addon_version            = "v1.24.1-eksbuild.1"
      resolve_conflicts        = "OVERWRITE"
      service_account_role_arn = module.iam.ebs_csi_driver_role_arn
    }
  }

  # IRSA
  enable_irsa = true

  depends_on = [
    module.iam,
    module.vpc
  ]
}

# IAM Module (with OIDC provider information)
module "iam" {
  source = "../../modules/iam"

  project_name = var.project_name
  environment  = var.environment
  common_tags  = local.common_tags

  cluster_name       = local.cluster_name
  oidc_provider_arn  = module.eks.oidc_provider_arn
  oidc_provider_url  = module.eks.oidc_provider_url

  kms_key_arn = aws_kms_key.main.arn

  enable_cluster_autoscaler           = var.enable_cluster_autoscaler
  enable_aws_load_balancer_controller = var.enable_aws_load_balancer_controller
  enable_external_dns                 = var.enable_external_dns
  enable_cert_manager                 = var.enable_cert_manager
  enable_ebs_csi_driver              = var.enable_ebs_csi_driver

  route53_zone_arns = var.route53_zone_arns
}



# RDS PostgreSQL + TimescaleDB Module
module "rds" {
  source = "../../modules/rds"

  db_identifier = "${var.project_name}-db-${var.environment}"
  common_tags   = local.common_tags

  # Engine Configuration
  engine_version       = var.postgres_version
  engine_version_major = split(".", var.postgres_version)[0]
  instance_class       = var.db_instance_class

  # Storage Configuration
  allocated_storage     = var.db_allocated_storage
  max_allocated_storage = var.db_max_allocated_storage
  storage_type          = "gp3"
  storage_encrypted     = var.enable_encryption_at_rest
  kms_key_id           = aws_kms_key.main.arn

  # Database Configuration
  database_name    = var.database_name
  master_username  = var.database_username
  master_password  = random_password.database_password.result

  # Network Configuration
  subnet_ids             = module.vpc.database_subnets
  vpc_security_group_ids = [module.vpc.database_security_group_id]
  db_subnet_group_name   = "${var.project_name}-db-subnet-group-${var.environment}"
  publicly_accessible    = false

  # Backup Configuration
  backup_retention_period = var.db_backup_retention_period
  backup_window          = var.db_backup_window
  maintenance_window     = var.db_maintenance_window

  # High Availability
  multi_az = var.db_multi_az

  # Monitoring Configuration
  monitoring_interval                        = var.enable_enhanced_monitoring ? 60 : 0
  monitoring_role_arn                       = aws_iam_role.rds_monitoring.arn
  performance_insights_enabled              = var.enable_performance_insights
  performance_insights_kms_key_id          = aws_kms_key.main.arn
  performance_insights_retention_period     = 7

  # CloudWatch Logs
  enabled_cloudwatch_logs_exports = ["postgresql", "upgrade"]
  cloudwatch_log_retention_days   = var.cloudwatch_log_retention_days
  cloudwatch_log_kms_key_id      = aws_kms_key.main.arn

  # Security Configuration
  deletion_protection = var.db_deletion_protection
  skip_final_snapshot = false

  # Performance Optimization for Analytics Workloads
  max_connections         = "200"
  work_mem               = "32768"    # 32MB for analytics queries
  maintenance_work_mem   = "2097152"  # 2GB for maintenance operations
  effective_cache_size   = "12582912" # 12GB for r6g.xlarge

  # Secrets Manager Integration
  manage_master_user_password = true

  # CloudWatch Alarms
  create_cloudwatch_alarms = true
  alarm_actions           = []

  depends_on = [
    module.vpc,
    aws_kms_key.main
  ]
}

# RDS Monitoring Role
resource "aws_iam_role" "rds_monitoring" {
  name = "${var.project_name}-rds-monitoring-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "monitoring.rds.amazonaws.com"
        }
      }
    ]
  })

  tags = local.common_tags
}

resource "aws_iam_role_policy_attachment" "rds_monitoring" {
  role       = aws_iam_role.rds_monitoring.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
}

# ElastiCache Redis Cluster Module
module "elasticache" {
  source = "../../modules/elasticache"

  replication_group_id = "${var.project_name}-cache-${var.environment}"
  description         = "Redis cluster for ${var.project_name} ${var.environment}"
  common_tags         = local.common_tags

  # Engine Configuration
  engine_version        = "7.0"
  engine_version_family = "7.x"
  node_type            = var.redis_node_type
  port                 = 6379

  # Cluster Configuration
  cluster_mode_enabled    = false
  num_cache_clusters      = var.redis_num_cache_nodes
  num_node_groups         = 1
  replicas_per_node_group = var.redis_num_cache_nodes - 1

  # Network Configuration
  subnet_ids         = module.vpc.database_subnets
  security_group_ids = [module.vpc.elasticache_security_group_id]
  subnet_group_name  = "${var.project_name}-cache-subnet-group-${var.environment}"

  # Security Configuration
  at_rest_encryption_enabled = var.enable_encryption_at_rest
  transit_encryption_enabled = var.enable_encryption_in_transit
  kms_key_id                 = aws_kms_key.main.arn

  # Backup Configuration
  snapshot_retention_limit = var.redis_snapshot_retention_limit
  snapshot_window         = var.redis_snapshot_window
  final_snapshot_identifier = null

  # Maintenance Configuration
  maintenance_window         = "sun:05:00-sun:09:00"
  auto_minor_version_upgrade = true

  # High Availability Configuration
  automatic_failover_enabled = var.redis_num_cache_nodes > 1
  multi_az_enabled           = var.redis_num_cache_nodes > 1

  # Performance Optimization
  maxmemory_policy = "allkeys-lru"
  timeout         = "300"
  tcp_keepalive   = "300"

  # Connection Pooling Configuration
  connection_pool_config = {
    max_connections    = 100
    min_connections    = 10
    connection_timeout = 5000
    idle_timeout      = 300000
  }

  # Monitoring Configuration
  create_cloudwatch_alarms = true
  alarm_actions           = []

  # Log Delivery Configuration
  log_delivery_configuration = [
    {
      destination      = "/aws/elasticache/${var.project_name}-cache-${var.environment}/slow-log"
      destination_type = "cloudwatch-logs"
      log_format       = "text"
      log_type         = "slow-log"
    }
  ]

  cloudwatch_log_retention_days = var.cloudwatch_log_retention_days
  cloudwatch_log_kms_key_id    = aws_kms_key.main.arn

  # Secrets Manager Integration
  manage_auth_token = true

  depends_on = [
    module.vpc,
    aws_kms_key.main
  ]
}
